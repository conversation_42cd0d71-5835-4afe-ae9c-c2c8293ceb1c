// pages/favorites/favorites.js
// 收藏夹页面

const app = getApp();
// 引入认证工具函数
const { isLoggedIn, getLocalData, syncLocalDataToServer } = require('../../utils/auth.js');

// 直接定义 mock 数据，避免模块导入问题
const mockContent = [
  { id: 1, name: '超赞的日料店', description: '刺身新鲜，天妇罗酥脆...', image: '/images/post1.jpg' },
  { id: 2, name: '火锅爱好者必去', description: '没有什么是一顿火锅解决不了的！', image: '/images/hotpot.jpg' }
];

const mockRestaurants = [
  { id: 3, name: 'Sushi Master', description: '城中最正宗的寿司', image: '/images/japanese.jpg' },
  { id: 4, name: '老王烧烤', description: '烟火气十足的街边美味', image: '/images/dish3.jpg' }
];

Page({
  data: {
    activeTab: 'content', // 'content' or 'restaurants'
    favorites: [],
    mockContent: mockContent,
    mockRestaurants: mockRestaurants,
    searchKeyword: '', // 搜索关键词
    filteredFavorites: [], // 过滤后的收藏列表
    showSearch: false, // 是否显示搜索框
    sortType: 'time', // 排序类型：time, name
    sortOrder: 'desc', // 排序顺序：asc, desc
    editMode: false, // 编辑模式
    selectedItems: [] // 选中的项目
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadFavorites();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新收藏列表，可能有新的收藏或取消收藏
    this.refreshFavorites();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshFavorites();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreFavorites();
  },

  /**
   * 加载收藏数据
   * 支持本地存储和服务器数据的合并显示
   */
  loadFavorites() {
    try {
      let allFavorites = [];
      
      // 检查登录状态
      if (isLoggedIn()) {
        // 已登录：从服务器加载数据
        this.loadServerFavorites();
        
        // 同步本地数据到服务器
        this.syncLocalFavoritesToServer();
      } else {
        // 未登录：只显示本地数据
        const localFavorites = this.loadLocalFavorites();
        allFavorites = localFavorites;
      }
      
      // 如果没有任何数据，生成模拟数据用于演示
      if (allFavorites.length === 0) {
        this.generateMockFavorites();
      } else {
        this.setData({
          favorites: allFavorites
        });
        this.filterFavorites();
      }
      
    } catch (error) {
      console.error('加载收藏数据失败:', error);
      this.generateMockFavorites();
    }
  },

  /**
   * 加载本地存储的收藏数据
   * Load local stored favorites data
   */
  loadLocalFavorites() {
    try {
      // 获取本地收藏数据
      const localCollects = getLocalData('userCollects');
      const storedFavorites = wx.getStorageSync('favorites') || [];
      
      // 转换本地收藏数据格式
      const convertedLocalData = localCollects
        .filter(item => item.isCollected && item.postData)
        .map(item => ({
          id: item.postId,
          name: item.postData.user?.userName || '未知用户',
          description: item.postData.content || '',
          image: item.postData.imageUrl || '/images/default.jpg',
          type: 'content',
          time: new Date(item.timestamp).toISOString(),
          location: item.postData.location,
          source: 'local' // 标记数据来源
        }));
      
      // 合并原有存储的收藏和新的本地数据
      const allLocalFavorites = [...storedFavorites, ...convertedLocalData];
      
      // 去重（基于id）
      const uniqueFavorites = allLocalFavorites.reduce((acc, current) => {
        const existingItem = acc.find(item => item.id === current.id);
        if (!existingItem) {
          acc.push(current);
        }
        return acc;
      }, []);
      
      console.log('本地收藏数据加载完成:', uniqueFavorites.length, '条');
      return uniqueFavorites;
      
    } catch (error) {
      console.error('加载本地收藏数据失败:', error);
      return [];
    }
  },

  /**
   * 从服务器加载收藏数据
   * Load favorites data from server
   */
  loadServerFavorites() {
    // TODO: 实现服务器数据加载
    console.log('从服务器加载收藏数据');
    
    // 示例代码：
    // wx.request({
    //   url: `${app.globalData.apiBaseUrl}/user/favorites`,
    //   method: 'GET',
    //   header: {
    //     'Authorization': `Bearer ${app.globalData.token}`
    //   },
    //   success: (res) => {
    //     if (res.data && res.data.success) {
    //       this.setData({
    //         favorites: res.data.data
    //       });
    //       this.filterFavorites();
    //     }
    //   },
    //   fail: (error) => {
    //     console.error('加载服务器收藏数据失败:', error);
    //     // 失败时加载本地数据
    //     const localFavorites = this.loadLocalFavorites();
    //     this.setData({ favorites: localFavorites });
    //     this.filterFavorites();
    //   }
    // });
  },

  /**
   * 同步本地收藏数据到服务器
   * Sync local favorites data to server
   */
  syncLocalFavoritesToServer() {
    if (!isLoggedIn()) {
      return;
    }
    
    // 同步收藏数据
    syncLocalDataToServer('userCollects', (collectData) => {
      return this.syncSingleCollectToServer(collectData);
    }).then(() => {
      console.log('本地收藏数据同步完成');
    }).catch(error => {
      console.error('同步本地收藏数据失败:', error);
    });
  },

  /**
   * 同步单个收藏项到服务器
   * Sync single collect item to server
   */
  syncSingleCollectToServer(collectData) {
    return new Promise((resolve, reject) => {
      // TODO: 实现单个收藏项的服务器同步
      console.log('同步单个收藏项到服务器:', collectData);
      
      // 示例代码：
      // wx.request({
      //   url: `${app.globalData.apiBaseUrl}/posts/${collectData.postId}/collect`,
      //   method: collectData.isCollected ? 'POST' : 'DELETE',
      //   data: collectData,
      //   header: {
      //     'Authorization': `Bearer ${app.globalData.token}`
      //   },
      //   success: (res) => {
      //     resolve(res);
      //   },
      //   fail: (error) => {
      //     reject(error);
      //   }
      // });
      
      // 临时解决方案：直接resolve
      setTimeout(() => resolve(), 100);
    });
  },

  /**
   * 生成模拟收藏数据
   */
  generateMockFavorites() {
    const types = ['food', 'post', 'user'];
    const foods = ['宫保鸡丁', '麻婆豆腐', '红烧肉', '糖醋里脊', '鱼香肉丝', '回锅肉'];
    const users = ['美食达人小王', '厨艺高手小李', '吃货小张', '料理专家小陈'];
    
    const favorites = [];
    
    for (let i = 1; i <= 50; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      
      let item = {
        id: i,
        type: type,
        createTime: createTime.toISOString(),
        favoriteTime: new Date(createTime.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      };
      
      switch (type) {
        case 'food':
          item = {
            ...item,
            title: foods[Math.floor(Math.random() * foods.length)],
            description: '这道菜色香味俱全，制作简单，营养丰富，是家常菜的经典选择。',
            image: `/images/dish${(i % 4) + 1}.jpg`,
            tags: ['家常菜', '下饭菜', '简单易做'],
            rating: (4 + Math.random()).toFixed(1),
            cookTime: Math.floor(Math.random() * 60 + 15) + '分钟'
          };
          break;
          
        case 'post':
          item = {
            ...item,
            title: `今天做了${foods[Math.floor(Math.random() * foods.length)]}`,
            content: '分享一下今天的美食制作过程，味道真的很棒！大家也可以试试看。',
            images: [`/images/post1.jpg`, `/images/feed${(i % 2) + 1}.png`],
            author: {
              name: users[Math.floor(Math.random() * users.length)],
              avatar: `/images/avatar${(i % 2) + 1}.png`
            },
            likes: Math.floor(Math.random() * 500),
            comments: Math.floor(Math.random() * 100)
          };
          break;
          
        case 'user':
          item = {
            ...item,
            name: users[Math.floor(Math.random() * users.length)],
            avatar: `/images/avatar${(i % 2) + 1}.png`,
            bio: '热爱美食，喜欢分享各种美味佳肴的制作方法。',
            followers: Math.floor(Math.random() * 10000),
            posts: Math.floor(Math.random() * 200),
            verified: Math.random() > 0.7
          };
          break;
      }
      
      favorites.push(item);
    }
    
    return favorites.sort((a, b) => new Date(b.favoriteTime) - new Date(a.favoriteTime));
  },

  /**
   * 刷新收藏列表
   */
  refreshFavorites() {
    this.setData({ 
      refreshing: true,
      page: 1
    });
    
    setTimeout(() => {
      this.loadFavorites();
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 加载更多收藏
   */
  loadMoreFavorites() {
    if (!this.data.hasMore || this.data.loading) return;
    
    this.setData({ loading: true });
    
    // 模拟加载更多数据
    setTimeout(() => {
      this.setData({
        loading: false,
        hasMore: false // 模拟没有更多数据
      });
    }, 1000);
  },

  /**
   * 切换标签页
   */
  onTabChange(e) {
    const { index } = e.currentTarget.dataset;
    this.setData({ activeTab: index });
    this.filterFavorites();
  },

  /**
   * 过滤收藏列表
   */
  filterFavorites() {
    const { activeTab, favorites, searchKeyword } = this.data;
    let filtered = [...favorites];
    
    // 按类型过滤
    if (activeTab > 0) {
      const typeMap = ['', 'food', 'post', 'user'];
      const targetType = typeMap[activeTab];
      filtered = filtered.filter(item => item.type === targetType);
    }
    
    // 按搜索关键词过滤 - 添加安全检查
    if (searchKeyword && searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(item => {
        const searchText = (item.title || item.name || item.content || '').toLowerCase();
        return searchText.includes(keyword);
      });
    }
    
    // 排序
    this.sortFavorites(filtered);
    
    this.setData({ filteredFavorites: filtered });
  },

  /**
   * 排序收藏列表
   */
  sortFavorites(list) {
    const { sortType, sortOrder } = this.data;
    
    list.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortType) {
        case 'time':
          valueA = new Date(a.favoriteTime);
          valueB = new Date(b.favoriteTime);
          break;
        case 'name':
          valueA = (a.title || a.name || '').toLowerCase();
          valueB = (b.title || b.name || '').toLowerCase();
          break;
        case 'type':
          valueA = a.type;
          valueB = b.type;
          break;
        default:
          return 0;
      }
      
      if (valueA < valueB) return sortOrder === 'asc' ? -1 : 1;
      if (valueA > valueB) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    
    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.filterFavorites();
    }, 300);
  },

  /**
   * 切换搜索框显示
   */
  onToggleSearch() {
    const searchVisible = !this.data.searchVisible;
    this.setData({ 
      searchVisible,
      searchKeyword: searchVisible ? this.data.searchKeyword : ''
    });
    
    if (!searchVisible) {
      this.filterFavorites();
    }
  },

  /**
   * 切换排序
   */
  onToggleSort() {
    const options = [
      { type: 'time', order: 'desc', label: '收藏时间（新到旧）' },
      { type: 'time', order: 'asc', label: '收藏时间（旧到新）' },
      { type: 'name', order: 'asc', label: '名称（A到Z）' },
      { type: 'name', order: 'desc', label: '名称（Z到A）' },
      { type: 'type', order: 'asc', label: '类型分组' }
    ];
    
    wx.showActionSheet({
      itemList: options.map(item => item.label),
      success: (res) => {
        const selected = options[res.tapIndex];
        this.setData({
          sortType: selected.type,
          sortOrder: selected.order
        });
        this.filterFavorites();
      }
    });
  },

  /**
   * 切换编辑模式
   */
  onToggleEdit() {
    const editMode = !this.data.editMode;
    this.setData({ 
      editMode,
      selectedItems: []
    });
  },

  /**
   * 选择收藏项
   */
  onSelectItem(e) {
    if (!this.data.editMode) return;
    
    const { id } = e.currentTarget.dataset;
    const { selectedItems } = this.data;
    
    const index = selectedItems.indexOf(id);
    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(id);
    }
    
    this.setData({ selectedItems });
  },

  /**
   * 全选/取消全选
   */
  onSelectAll() {
    const { filteredFavorites, selectedItems } = this.data;
    const allIds = filteredFavorites.map(item => item.id);
    
    if (selectedItems.length === allIds.length) {
      // 取消全选
      this.setData({ selectedItems: [] });
    } else {
      // 全选
      this.setData({ selectedItems: allIds });
    }
  },

  /**
   * 批量删除收藏
   */
  onBatchDelete() {
    const { selectedItems } = this.data;
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的项目',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '删除收藏',
      content: `确定要删除选中的 ${selectedItems.length} 个收藏吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteFavorites(selectedItems);
        }
      }
    });
  },

  /**
   * 删除收藏
   */
  deleteFavorites(ids) {
    wx.showLoading({ title: '删除中...' });
    
    setTimeout(() => {
      const favorites = this.data.favorites.filter(item => !ids.includes(item.id));
      
      this.setData({
        favorites,
        selectedItems: [],
        editMode: false,
        totalCount: favorites.length
      });
      
      this.filterFavorites();
      
      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 点击收藏项
   */
  onFavoriteItemTap(e) {
    if (this.data.editMode) {
      this.onSelectItem(e);
      return;
    }
    
    const { item } = e.currentTarget.dataset;
    
    // 根据类型跳转到对应页面
    switch (item.type) {
      case 'food':
        wx.showToast({
          title: '跳转到美食详情',
          icon: 'none'
        });
        break;
      case 'post':
        wx.showToast({
          title: '跳转到动态详情',
          icon: 'none'
        });
        break;
      case 'user':
        wx.showToast({
          title: '跳转到用户主页',
          icon: 'none'
        });
        break;
    }
  },

  /**
   * 取消收藏
   */
  onUnfavorite(e) {
    e.stopPropagation();
    const { id } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '取消收藏',
      content: '确定要取消收藏这个项目吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteFavorites([id]);
        }
      }
    });
  },

  /**
   * 分享收藏
   */
  onShareFavorite(e) {
    e.stopPropagation();
    const { item } = e.currentTarget.dataset;
    
    wx.showShareMenu({
      withShareTicket: true,
      success: () => {
        wx.showToast({
          title: '分享成功',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    
    if (diff < minute) {
      return '刚刚';
    } else if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前';
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前';
    } else if (diff < 7 * day) {
      return Math.floor(diff / day) + '天前';
    } else {
      return time.toLocaleDateString();
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  }
});