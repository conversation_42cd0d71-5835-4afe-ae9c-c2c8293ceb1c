// pages/my-posts/my-posts.js
// 我的动态页面

const app = getApp();

// 直接定义 mock 数据，避免模块导入问题
const mockPosts = [
  { id: 1, date: '2024-07-21', content: '今天发现一家超赞的日料店，刺身新鲜，天妇罗酥脆，幸福感满满！🍣', image: '/images/post1.jpg', likes: 102, comments: 34 },
  { id: 2, date: '2024-07-20', content: '没有什么是一顿火锅解决不了的！如果有，那就两顿！🌶️🌶️🌶️', image: '/images/hotpot.jpg', likes: 88, comments: 21 }
];

Page({
  data: {
    posts: []
  },

  onShow: function () {
    this.loadPosts();
  },

  loadPosts: function () {
    this.setData({ posts: mockPosts });
  },

  /**
   * 生成模拟动态数据
   */
  generateMockPosts() {
    const posts = [];
    const statuses = ['published', 'draft'];
    const titles = [
      '今天尝试了新的川菜馆',
      '自制的红烧肉，味道不错',
      '推荐一家性价比很高的日料店',
      '周末在家做的蛋糕',
      '发现了一家隐藏的小吃店'
    ];
    
    for (let i = 0; i < this.data.pageSize; i++) {
      const randomIndex = Math.floor(Math.random() * titles.length);
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      posts.push({
        id: Date.now() + i,
        title: titles[randomIndex],
        content: '这是一段关于美食的精彩分享内容，包含了详细的体验和推荐...',
        images: [
          '/images/dish1.jpg',
          '/images/dish2.jpg'
        ],
        status: status,
        createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString(),
        likeCount: Math.floor(Math.random() * 100),
        commentCount: Math.floor(Math.random() * 50),
        viewCount: Math.floor(Math.random() * 500)
      });
    }
    
    return posts;
  },

  /**
   * 根据标签页过滤动态
   */
  filterPostsByTab(posts) {
    if (this.data.activeTab === 'all') {
      return posts;
    }
    return posts.filter(post => post.status === this.data.activeTab);
  },

  /**
   * 更新标签页计数
   */
  updateTabCounts() {
    const allPosts = this.data.posts;
    const tabs = this.data.tabs.map(tab => {
      let count = 0;
      if (tab.key === 'all') {
        count = allPosts.length;
      } else {
        count = allPosts.filter(post => post.status === tab.key).length;
      }
      return { ...tab, count };
    });
    
    this.setData({ tabs });
  },

  /**
   * 切换标签页
   */
  onTabChange(e) {
    const tabKey = e.currentTarget.dataset.tab;
    if (tabKey === this.data.activeTab) return;
    
    this.setData({
      activeTab: tabKey
    });
    
    this.refreshPosts();
  },

  /**
   * 查看动态详情
   */
  onViewPost(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${postId}`
    });
  },

  /**
   * 编辑动态
   */
  onEditPost(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/publish/publish?id=${postId}&mode=edit`
    });
  },

  /**
   * 显示动态操作菜单
   */
  onShowPostActions(e) {
    const postId = e.currentTarget.dataset.id;
    const post = this.data.posts.find(p => p.id == postId);
    
    this.setData({
      selectedPost: post,
      showActionSheet: true
    });
  },

  /**
   * 隐藏操作菜单
   */
  onHideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedPost: null
    });
  },

  /**
   * 删除动态
   */
  onDeletePost() {
    if (!this.data.selectedPost) return;
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条动态吗？',
      success: (res) => {
        if (res.confirm) {
          this.deletePost(this.data.selectedPost.id);
        }
      }
    });
    
    this.onHideActionSheet();
  },

  /**
   * 执行删除动态
   */
  deletePost(postId) {
    wx.showLoading({ title: '删除中...' });
    
    // 模拟删除API请求
    setTimeout(() => {
      const posts = this.data.posts.filter(post => post.id !== postId);
      this.setData({ posts });
      this.updateTabCounts();
      
      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 分享动态
   */
  onSharePost() {
    if (!this.data.selectedPost) return;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    this.onHideActionSheet();
  },

  /**
   * 发布新动态
   */
  onCreatePost() {
    wx.navigateTo({
      url: '/pages/publish/publish'
    });
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return Math.floor(diff / 86400000) + '天前';
    }
  }
});