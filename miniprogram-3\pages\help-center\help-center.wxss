/* pages/help-center/help-center.wxss */
/* 帮助中心页面样式 - 黑白极简设计 */

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: #F0F2F5;
  color: #333333;
  line-height: 1.6;
}

.container {
  max-width: 750rpx;
  margin: 0 auto;
  background-color: #F0F2F5;
  min-height: 100vh;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E0E0E0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-back {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.nav-back-icon {
  font-size: 36rpx;
  color: #333333;
  font-weight: 300;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  text-align: center;
}

.nav-placeholder {
  width: 80rpx;
}

/* 搜索栏 */
.search-container {
  padding: 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E0E0E0;
}

.search-box {
  display: flex;
  align-items: center;
  background: #F8F9FA;
  border: 1rpx solid #E0E0E0;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #666666;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.clear-icon {
  font-size: 32rpx;
  color: #999999;
}

/* 热门问题 */
.hot-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 16rpx;
  border: 1rpx solid #E0E0E0;
  overflow: hidden;
}

.section-title {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #F0F2F5;
  background: #FAFAFA;
}

.title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.hot-questions {
  padding: 16rpx 0;
}

.hot-item {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hot-item:hover {
  background: #F8F9FA;
}

.hot-number {
  width: 48rpx;
  height: 48rpx;
  background: #000000; /* 改为黑色背景 */
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
}

.hot-question {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
  line-height: 1.4;
}

.hot-count {
  font-size: 22rpx;
  color: #999999;
}

/* 分类标签 */
.categories-container {
  padding: 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E0E0E0;
}

.categories {
  white-space: nowrap;
}

.category-item {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  background: #F8F9FA;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-item.active {
  background: #000000; /* 改为黑色背景 */
  border-color: #000000; /* 改为黑色边框 */
  color: #ffffff;
}

.category-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.category-label {
  font-size: 24rpx;
  white-space: nowrap;
}

/* 帮助列表 */
.help-list {
  padding: 32rpx;
}

/* 无结果状态 */
.no-results {
  text-align: center;
  padding: 120rpx 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 1rpx solid #E0E0E0;
}

.no-results-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.no-results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.no-results-desc {
  font-size: 26rpx;
  color: #666666;
}

/* 帮助条目 */
.help-item {
  background: #ffffff;
  border-radius: 16rpx;
  border: 1rpx solid #E0E0E0;
  margin-bottom: 24rpx;
  overflow: hidden;
  transition: all 0.2s ease;
}

.help-item:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.help-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  cursor: pointer;
  border-bottom: 1rpx solid #F0F2F5;
}

.help-question {
  flex: 1;
}

.question-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: block;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.category-tag {
  background: #F0F2F5;
  color: #666666;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.view-count {
  font-size: 22rpx;
  color: #999999;
}

.help-toggle {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666666;
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 答案内容 */
.help-answer {
  padding: 0 32rpx 32rpx;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.answer-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
  white-space: pre-line;
}

.answer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.answer-tag {
  background: #f5f5f5; /* 改为浅灰色背景 */
  color: #000000; /* 改为黑色文字 */
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.answer-actions {
  display: flex;
  gap: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #F0F2F5;
}

.action-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-item:hover {
  opacity: 0.7;
}

.action-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666666;
}

/* 联系客服 */
.contact-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 16rpx;
  border: 1rpx solid #E0E0E0;
  overflow: hidden;
}

.contact-info {
  padding: 16rpx 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1rpx solid #F0F2F5;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:hover {
  background: #F8F9FA;
}

.contact-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: #F0F2F5;
  border-radius: 50%;
  margin-right: 24rpx;
}

.contact-content {
  flex: 1;
}

.contact-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 24rpx;
  color: #666666;
}

.contact-arrow {
  color: #CCCCCC;
  font-size: 24rpx;
}

.work-time {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: #F8F9FA;
  border-top: 1rpx solid #E0E0E0;
}

.work-time-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.work-time-text {
  font-size: 24rpx;
  color: #666666;
}

/* 底部安全区域 */
.safe-area {
  height: 68rpx;
  background: #F0F2F5;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .hot-number {
    width: 40rpx;
    height: 40rpx;
    font-size: 20rpx;
  }
  
  .hot-question {
    font-size: 24rpx;
  }
  
  .category-item {
    padding: 12rpx 20rpx;
    margin-right: 12rpx;
  }
}