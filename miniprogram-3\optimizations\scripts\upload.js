/**
 * 小程序上传脚本
 * 自动化小程序代码上传流程
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class MiniProgramUploader {
  constructor() {
    this.projectPath = path.resolve(__dirname, '..');
    this.configPath = path.join(this.projectPath, 'project.config.json');
  }

  /**
   * 执行上传
   * @param {Object} options 上传选项
   */
  async upload(options = {}) {
    const {
      version = '1.0.0',
      desc = '自动上传',
      robot = 1,
      threads = 4
    } = options;

    console.log('🚀 开始上传小程序...');
    console.log('版本: ' + version);
    console.log('描述: ' + desc);
    console.log('机器人: ' + robot);

    try {
      // 检查项目配置
      this.checkProjectConfig();

      // 构建命令
      const command = [
        'wx',
        'upload',
        '--project "' + this.projectPath + '"',
        '--version "' + version + '"',
        '--desc "' + desc + '"',
        '--robot ' + robot,
        '--threads ' + threads
      ].join(' ');

      console.log('执行命令:', command);

      // 执行上传
      const result = execSync(command, {
        encoding: 'utf8',
        cwd: this.projectPath,
        stdio: 'inherit'
      });

      console.log('✅ 上传成功!');
      return result;

    } catch (error) {
      console.error('❌ 上传失败:', error.message);
      throw error;
    }
  }

  /**
   * 检查项目配置
   */
  checkProjectConfig() {
    if (!fs.existsSync(this.configPath)) {
      throw new Error('project.config.json 文件不存在');
    }

    const config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
    
    if (!config.appid) {
      throw new Error('project.config.json 中缺少 appid 配置');
    }

    console.log('项目 ID: ' + config.appid);
  }

  /**
   * 获取版本号
   * @returns {string} 版本号
   */
  getVersion() {
    const packagePath = path.join(this.projectPath, 'package.json');
    if (fs.existsSync(packagePath)) {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      return packageJson.version || '1.0.0';
    }
    return '1.0.0';
  }
}

// 命令行调用
if (require.main === module) {
  const uploader = new MiniProgramUploader();
  
  // 解析命令行参数
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    options[key] = value;
  }
  
  // 设置默认版本号
  if (!options.version) {
    options.version = uploader.getVersion();
  }
  
  uploader.upload(options)
    .then(() => {
      console.log('🎉 上传完成!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 上传失败:', error.message);
      process.exit(1);
    });
}

module.exports = MiniProgramUploader;
