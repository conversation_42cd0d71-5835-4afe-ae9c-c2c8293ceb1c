/* styles/design-system.wxss */
/* 统一设计系统 - 字体和框架结构标准化 */

/* ==================== 字体层级系统 ==================== */
/* 标题层级 */
.text-h1 { font-size: 40rpx; font-weight: 700; line-height: 1.2; color: #000000; } /* 页面主标题 */
.text-h2 { font-size: 36rpx; font-weight: 600; line-height: 1.3; color: #000000; } /* 区域标题 */
.text-h3 { font-size: 32rpx; font-weight: 600; line-height: 1.4; color: #000000; } /* 卡片标题 */

/* 正文层级 */
.text-body-large { font-size: 32rpx; font-weight: 500; line-height: 1.5; color: #000000; } /* 重要正文 */
.text-body { font-size: 30rpx; font-weight: 400; line-height: 1.6; color: #000000; } /* 标准正文 */
.text-body-small { font-size: 28rpx; font-weight: 400; line-height: 1.5; color: #333333; } /* 次要正文 */

/* 辅助文字层级 */
.text-caption { font-size: 26rpx; font-weight: 400; line-height: 1.4; color: #666666; } /* 说明文字 */
.text-small { font-size: 24rpx; font-weight: 400; line-height: 1.3; color: #888888; } /* 提示文字 */
.text-micro { font-size: 22rpx; font-weight: 400; line-height: 1.2; color: #999999; } /* 极小文字 */

/* ==================== 边框和圆角系统 ==================== */
/* 边框粗细 */
.border-hairline { border-width: 0.5rpx; } /* 极细边框 */
.border-thin { border-width: 1rpx; } /* 细边框 */
.border-normal { border-width: 2rpx; } /* 标准边框 */
.border-thick { border-width: 4rpx; } /* 粗边框 */

/* 边框颜色 */
.border-light { border-color: #f0f0f0; } /* 浅色边框 */
.border-gray { border-color: #e0e0e0; } /* 灰色边框 */
.border-dark { border-color: #cccccc; } /* 深灰边框 */
.border-black { border-color: #000000; } /* 黑色边框 */

/* 圆角大小 */
.radius-none { border-radius: 0; } /* 无圆角 */
.radius-small { border-radius: 8rpx; } /* 小圆角 */
.radius-normal { border-radius: 12rpx; } /* 标准圆角 */
.radius-large { border-radius: 16rpx; } /* 大圆角 */
.radius-xlarge { border-radius: 24rpx; } /* 超大圆角 */
.radius-round { border-radius: 50%; } /* 圆形 */

/* ==================== 间距系统 ==================== */
/* 间距变量 */
:root {
  --spacing-xs: 8rpx;   /* 极小间距 */
  --spacing-sm: 12rpx;  /* 小间距 */
  --spacing-md: 16rpx;  /* 标准间距 */
  --spacing-lg: 20rpx;  /* 大间距 */
  --spacing-xl: 24rpx;  /* 超大间距 */
  --spacing-2xl: 32rpx; /* 特大间距 */
  --spacing-3xl: 40rpx; /* 巨大间距 */
}

/* 间距类 */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* ==================== 基础布局系统 ==================== */
/* 容器和基础布局 */
.container {
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color: #000000;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-header {
  background-color: #ffffff;
  border-bottom: 2rpx solid #000000;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #000000;
}

/* 主内容区域 */
.content-scroll {
  flex: 1;
  background-color: #ffffff;
}

.main-content {
  padding: 30rpx;
}

/* 通用卡片样式 */
.card {
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
}

/* 通用按钮样式 */
.button {
  display: block;
  width: 100%;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: none;
  box-sizing: border-box;
}

.button.primary {
  background-color: #000000;
  color: #ffffff;
}

.button.secondary {
  background-color: #f0f0f0;
  color: #333333;
}

.button:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 通用标签样式 */
.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #1890ff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}
