/* pages/result/result.wxss */
page {
    background-color: #ffffff;
}

.phone-container {
    padding: 16px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    box-sizing: border-box;
}

.content-wrapper {
    flex-grow: 1;
}

/* 1. 顶部标题栏 */
.title-bar {
    text-align: center;
    padding-top: 2%;
    padding-bottom: 6%;
}
.title-bar .title {
    font-size: 40rpx; /* 统一字体：px转rpx，调整为H1标准 */
    font-weight: 600;
    margin: 0;
}

/* 2. 主推菜品区 */
.main-dish-area {
    padding: 40rpx; /* 统一间距：px转rpx */
    text-align: center;
    border: 2rpx solid #000000; /* 统一边框：px转rpx，调整为标准边框 */
    border-radius: 12rpx; /* 统一圆角：px转rpx，调整为标准圆角 */
    margin-bottom: 5%;
}
.dish-card-simple {
    font-size: 32rpx; /* 统一字体：px转rpx，调整为H3标准 */
    font-weight: 600;
    margin-bottom: 15px;
}
.dish-card-core {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}
.dish-card-core .dish-name {
    font-size: 32rpx; /* 统一字体：px转rpx，调整为重要正文大小 */
    font-weight: 500;
}
.dish-card-core .dish-extra-info {
    display: flex;
    align-items: baseline;
    gap: 8px;
}
.dish-card-core .rating {
    color: #000000; /* 修改为黑色，保持黑白简约风格 */
    font-weight: bold;
}
.dish-card-core .sales {
    font-size: 24rpx; /* 统一字体：px转rpx，调整为提示文字大小 */
    color: #000000; /* 修改为黑色，保持黑白简约风格 */
    opacity: 0.7; /* 通过透明度区分重要性 */
}

/* 3. 菜品详情信息 */
.dish-details-tags {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-bottom: 6%;
    border-bottom: 10px solid #f5f5f5;
}
.tag-item {
    font-size: 26rpx; /* 统一字体：px转rpx，调整为说明文字大小 */
    color: #000000; /* 修改为黑色，保持黑白简约风格 */
}
.tag-item.price {
    color: #000000; /* 修改为黑色，保持黑白简约风格 */
    font-weight: bold;
}
.tag-item.promo {
    color: #000000; /* 修改为黑色，保持黑白简约风格 */
    font-weight: bold;
}
.separator {
    width: 2rpx; /* 统一边框：px转rpx，调整为标准边框粗细 */
    height: 40rpx; /* 统一尺寸：px转rpx */
    background-color: #000000; /* 修改为黑色，保持黑白简约风格 */
    opacity: 0.3; /* 通过透明度降低视觉强调 */
}

/* 4. 平台比价模块 */
.price-comparison-module {
    padding-top: 6%;
    padding-bottom: 6%;
    border-bottom: 10px solid #f5f5f5;
}
.module-title {
    font-size: 32rpx; /* 统一字体：px转rpx，调整为H3标准 */
    font-weight: 600;
    margin-bottom: 15px;
}
.comparison-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}
.platform-logo {
    width: 48rpx; /* 统一尺寸：px转rpx */
    height: 48rpx; /* 统一尺寸：px转rpx */
    border-radius: 50%;
    background-color: #f0f0f0; /* 保持浅灰色背景以区分区域 */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 22rpx; /* 统一字体：px转rpx，调整为极小文字大小 */
    font-weight: bold;
    margin-right: 24rpx; /* 统一间距：px转rpx */
}
.platform-info {
    flex-grow: 1;
}

.platform-name {
    font-size: 30rpx; /* 统一字体：px转rpx，调整为标准正文大小 */
}

.platform-price {
    font-size: 26rpx; /* 统一字体：px转rpx，调整为说明文字大小 */
    color: #000000; /* 修改为黑色，保持黑白简约风格 */
    opacity: 0.7; /* 通过透明度区分重要性 */
}
.order-button {
    padding: 16rpx 32rpx; /* 统一间距：px转rpx */
    background-color: #ffffff;
    color: #000000;
    text-decoration: none;
    border-radius: 32rpx; /* 统一圆角：px转rpx */
    font-size: 26rpx; /* 统一字体：px转rpx，调整为说明文字大小 */
    border: 2rpx solid #000000; /* 统一边框：px转rpx，调整为标准边框 */
}

/* 5. 社交推荐区 */
.social-proof-section {
    padding-top: 6%;
    padding-bottom: 8%;
}

.recommendation-cards {
    display: flex;
    justify-content: space-around;
}
.reco-card {
    text-align: center;
    width: 30%; /* Assign a width to ensure consistency */
}
.reco-card .reco-card-image {
    width: 100%;
    height: 180rpx; /* 统一尺寸：px转rpx */
    background-color: #f0f0f0; /* 保持浅灰色背景以区分区域 */
    border-radius: 12rpx; /* 统一圆角：px转rpx，调整为标准圆角 */
    margin-bottom: 16rpx; /* 统一间距：px转rpx */
}
.reco-card .dish-name {
    font-size: 28rpx; /* 统一字体：px转rpx，调整为次要正文大小 */
    font-weight: 500;
    margin-bottom: 8rpx; /* 统一间距：px转rpx */
}
.reco-card .user-name {
    font-size: 24rpx; /* 统一字体：px转rpx，调整为提示文字大小 */
    color: #000000; /* 修改为黑色，保持黑白简约风格 */
    opacity: 0.7; /* 通过透明度区分重要性 */
}

/* 6. 底部操作栏 */
.footer-actions {
    display: flex;
    justify-content: space-between;
    gap: 5%;
    padding-top: 3%;
}
.action-button {
    flex-grow: 1;
    padding: 24rpx 0; /* 统一间距：px转rpx */
    text-align: center;
    border-radius: 50rpx; /* 统一圆角：px转rpx */
    font-size: 32rpx; /* 统一字体：px转rpx，调整为重要正文大小 */
    font-weight: 500;
    /* 确保自定义样式覆盖button默认样式 */
    box-sizing: border-box; /* 确保padding和border不增加宽度 */
}

/* 重置button默认样式 */
button::after {
    border: none; /* 移除button默认边框 */
}
button {
    padding: 0; /* 移除button默认padding */
    margin: 0; /* 移除button默认margin */
    background-color: transparent; /* 移除button默认背景色 */
    line-height: normal; /* 恢复正常行高 */
    border-radius: 0; /* 移除button默认圆角 */
    font-size: inherit; /* 继承父级字体大小 */
    color: inherit; /* 继承父级字体颜色 */
}

.reroll-button {
    border: 2rpx solid #000000; /* 统一边框：px转rpx，调整为标准边框 */
    color: #000000;
    background-color: #ffffff;
}
.share-button {
    border: 2rpx solid #000000; /* 统一边框：px转rpx，调整为标准边框 */
    color: #000000;
    background-color: #ffffff;
}