<!--pages/user-info/user-info.wxml-->
<!-- 个人信息编辑页面 - 黑白极简设计 -->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="onBack">
      <text class="nav-back-icon">‹</text>
    </view>
    <text class="nav-title">编辑个人信息</text>
    <view class="nav-action" bindtap="onSave">
      <text class="nav-save">保存</text>
    </view>
  </view>

  <!-- 头像区域 -->
  <view class="avatar-section">
    <view class="avatar-container" bindtap="onEditAvatar">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}"></image>
      <view class="avatar-edit-overlay">
        <text class="avatar-edit-text">编辑</text>
      </view>
    </view>
    <view class="avatar-tip">点击更换头像</view>
  </view>

  <!-- 基本信息 -->
  <view class="info-section">
    <view class="section-title">基本信息</view>
    <view class="info-item" bindtap="onEditNickName">
      <text class="info-label">昵称</text>
      <text class="info-value">{{userInfo.nickName}}</text>
      <text class="info-arrow">›</text>
    </view>

    <!-- 调整顺序：生日在前，性别在后 -->
    <picker mode="date" value="{{userInfo.birthday}}" start="1920-01-01" end="2025-12-31" bindchange="onDateChange">
      <view class="info-item">
        <text class="info-label">生日</text>
        <text class="info-value">{{userInfo.birthday}}</text>
        <text class="info-arrow">›</text>
      </view>
    </picker>

    <picker mode="selector" range="{{genderOptions}}" range-key="label" value="{{userInfo.gender}}" bindchange="onGenderChange">
      <view class="info-item">
        <text class="info-label">性别</text>
        <text class="info-value">{{genderOptions[userInfo.gender].label}}</text>
        <text class="info-arrow">›</text>
      </view>
    </picker>

    <view class="info-item" bindtap="onEditSignature">
      <text class="info-label">个性签名</text>
      <text class="info-value signature">{{userInfo.signature || '未设置'}}</text>
      <text class="info-arrow">›</text>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="info-section">
    <view class="section-title">联系方式</view>
    <view class="info-item" bindtap="onEditPhone">
      <text class="info-label">手机号</text>
      <text class="info-value">{{userInfo.phone || '未绑定'}}</text>
      <text class="info-arrow">›</text>
    </view>
    <view class="info-item" bindtap="onEditEmail">
      <text class="info-label">邮箱</text>
      <text class="info-value">{{userInfo.email || '未绑定'}}</text>
      <text class="info-arrow">›</text>
    </view>
    <view class="info-item" bindtap="onEditLocation">
      <text class="info-label">地区</text>
      <text class="info-value">{{userInfo.location || '未设置'}}</text>
      <text class="info-arrow">›</text>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>
