/* pages/my-posts/my-posts.wxss */
/* 我的动态页面样式 - 黑白极简设计 */

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: #F0F2F5;
  color: #333333;
  line-height: 1.6;
}

.container {
  max-width: 750rpx;
  margin: 0 auto;
  background-color: #F0F2F5;
  min-height: 100vh;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E0E0E0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-back {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.nav-back-icon {
  font-size: 36rpx;
  color: #333333;
  font-weight: 300;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  text-align: center;
}

.nav-action {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.nav-create {
  font-size: 40rpx;
  color: #000000; /* 改为黑色文字 */
  font-weight: 300;
}

/* 标签页 */
.tabs-container {
  background: #ffffff;
  border-bottom: 1rpx solid #E0E0E0;
  padding: 0 32rpx;
}

.tabs {
  display: flex;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.tab-item.active {
  color: #000000; /* 改为黑色文字 */
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #000000; /* 改为黑色背景 */
  border-radius: 2rpx;
}

.tab-label {
  font-size: 28rpx;
  font-weight: 500;
}

.tab-count {
  font-size: 24rpx;
  color: #666666;
  margin-left: 8rpx;
}

/* 动态列表容器 */
.posts-container {
  padding: 32rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 1rpx solid #E0E0E0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.empty-action {
  background: #000000; /* 改为黑色背景 */
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 动态项 */
.post-item {
  background: #ffffff;
  border-radius: 16rpx;
  border: 1rpx solid #E0E0E0;
  margin-bottom: 24rpx;
  overflow: hidden;
  transition: all 0.2s ease;
}

.post-item:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 动态头部 */
.post-header {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #F0F2F5;
}

.post-status {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-dot.published {
  background: #000000; /* 改为黑色背景 */
}

.status-dot.draft {
  background: #666666; /* 改为灰色背景 */
}

.status-text {
  font-size: 24rpx;
  color: #666666;
}

.post-time {
  font-size: 24rpx;
  color: #999999;
  margin-right: 16rpx;
}

.post-actions {
  width: 60rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-icon {
  font-size: 32rpx;
  color: #666666;
  transform: rotate(90deg);
}

/* 动态内容 */
.post-content {
  padding: 16rpx 32rpx 24rpx;
  cursor: pointer;
}

.post-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.post-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 动态图片 */
.post-images {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.post-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 1rpx solid #E0E0E0;
}

/* 动态统计 */
.post-stats {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx 24rpx;
  border-top: 1rpx solid #F0F2F5;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
}

.stat-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.stat-count {
  font-size: 24rpx;
  color: #666666;
}

.post-edit {
  margin-left: auto;
  cursor: pointer;
}

.edit-text {
  font-size: 26rpx;
  color: #000000; /* 改为黑色文字 */
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666666;
}

.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999999;
}

/* 操作菜单 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.action-sheet {
  width: 100%;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.action-sheet-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E0E0E0;
}

.action-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.action-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666666;
  cursor: pointer;
}

.action-list {
  padding: 16rpx 0 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-item:hover {
  background: #F8F9FA;
}

.action-item.danger {
  color: #000000; /* 改为黑色文字 */
}

.action-item .action-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
  transform: none;
}

.action-item .action-text {
  font-size: 28rpx;
}

/* 底部安全区域 */
.safe-area {
  height: 68rpx;
  background: #F0F2F5;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .post-images {
    gap: 8rpx;
  }
  
  .post-image {
    width: 100rpx;
    height: 100rpx;
  }
  
  .stat-item {
    margin-right: 24rpx;
  }
}