/**
 * 云开发API工具类
 * 封装云函数调用，提供统一的API接口
 */

const { getEnvironmentConfig } = require('../config/environment');

/**
 * 云开发API类
 */
class CloudAPI {
  constructor() {
    this.config = getEnvironmentConfig();
    this.isCloudEnabled = this.config.useCloudFunction;
    
    if (this.isCloudEnabled) {
      // 初始化云开发
      wx.cloud.init({
        env: this.config.cloudEnvId,
        traceUser: true
      });
    }
  }
  
  /**
   * 检查是否启用云开发
   */
  isEnabled() {
    return this.isCloudEnabled;
  }
  
  /**
   * 调用云函数
   * @param {string} functionName 云函数名称
   * @param {object} data 传递给云函数的数据
   * @returns {Promise} 云函数执行结果
   */
  async callFunction(functionName, data = {}) {
    if (!this.isCloudEnabled) {
      throw new Error('云开发未启用');
    }
    
    try {
      console.log(`[CloudAPI] 调用云函数: ${functionName}`, data);
      
      const result = await wx.cloud.callFunction({
        name: functionName,
        data: data
      });
      
      console.log(`[CloudAPI] 云函数响应: ${functionName}`, result);
      
      if (result.errMsg !== 'cloud.callFunction:ok') {
        throw new Error(`云函数调用失败: ${result.errMsg}`);
      }
      
      return result.result;
    } catch (error) {
      console.error(`[CloudAPI] 云函数调用错误: ${functionName}`, error);
      throw error;
    }
  }
  
  /**
   * 用户认证相关API
   */
  auth = {
    /**
     * 用户登录
     * @param {object} userInfo 用户信息
     * @returns {Promise} 登录结果
     */
    login: async (userInfo) => {
      return await this.callFunction('auth', {
        action: 'login',
        data: { userInfo }
      });
    },
    
    /**
     * 获取用户信息
     * @returns {Promise} 用户信息
     */
    getUserInfo: async () => {
      return await this.callFunction('auth', {
        action: 'getUserInfo'
      });
    },
    
    /**
     * 更新用户信息
     * @param {object} userInfo 用户信息
     * @returns {Promise} 更新结果
     */
    updateUserInfo: async (userInfo) => {
      return await this.callFunction('auth', {
        action: 'updateUserInfo',
        data: userInfo
      });
    },
    
    /**
     * 更新用户偏好
     * @param {object} preferences 用户偏好
     * @returns {Promise} 更新结果
     */
    updatePreferences: async (preferences) => {
      return await this.callFunction('auth', {
        action: 'updatePreferences',
        data: { preferences }
      });
    }
  };
  
  /**
   * 餐厅相关API
   */
  restaurant = {
    /**
     * 获取餐厅列表
     * @param {object} params 查询参数
     * @returns {Promise} 餐厅列表
     */
    getList: async (params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'getRestaurants',
        data: params
      });
    },
    
    /**
     * 获取餐厅详情
     * @param {string} restaurantId 餐厅ID
     * @returns {Promise} 餐厅详情
     */
    getDetail: async (restaurantId) => {
      return await this.callFunction('restaurant', {
        action: 'getRestaurantDetail',
        data: { restaurantId }
      });
    },
    
    /**
     * 搜索餐厅
     * @param {string} keyword 搜索关键词
     * @param {object} params 其他参数
     * @returns {Promise} 搜索结果
     */
    search: async (keyword, params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'searchRestaurants',
        data: { keyword, ...params }
      });
    }
  };
  
  /**
   * 菜品相关API
   */
  dish = {
    /**
     * 获取菜品列表
     * @param {object} params 查询参数
     * @returns {Promise} 菜品列表
     */
    getList: async (params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'getDishes',
        data: params
      });
    },
    
    /**
     * 获取菜品详情
     * @param {string} dishId 菜品ID
     * @returns {Promise} 菜品详情
     */
    getDetail: async (dishId) => {
      return await this.callFunction('restaurant', {
        action: 'getDishDetail',
        data: { dishId }
      });
    },
    
    /**
     * 搜索菜品
     * @param {string} keyword 搜索关键词
     * @param {object} params 其他参数
     * @returns {Promise} 搜索结果
     */
    search: async (keyword, params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'searchDishes',
        data: { keyword, ...params }
      });
    }
  };
  
  /**
   * 推荐相关API
   */
  recommendation = {
    /**
     * 获取推荐内容
     * @param {string} type 推荐类型 (dish/restaurant)
     * @param {object} params 其他参数
     * @returns {Promise} 推荐结果
     */
    get: async (type = 'dish', params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'getRecommendations',
        data: { type, ...params }
      });
    }
  };
  
  /**
   * 收藏相关API
   */
  favorite = {
    /**
     * 添加收藏
     * @param {string} type 收藏类型 (dish/restaurant)
     * @param {string} itemId 项目ID
     * @returns {Promise} 添加结果
     */
    add: async (type, itemId) => {
      return await this.callFunction('restaurant', {
        action: 'addFavorite',
        data: { type, itemId }
      });
    },
    
    /**
     * 移除收藏
     * @param {string} type 收藏类型 (dish/restaurant)
     * @param {string} itemId 项目ID
     * @returns {Promise} 移除结果
     */
    remove: async (type, itemId) => {
      return await this.callFunction('restaurant', {
        action: 'removeFavorite',
        data: { type, itemId }
      });
    },
    
    /**
     * 获取收藏列表
     * @param {string} type 收藏类型 (dish/restaurant)
     * @param {object} params 其他参数
     * @returns {Promise} 收藏列表
     */
    getList: async (type, params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'getFavorites',
        data: { type, ...params }
      });
    }
  };
  
  /**
   * 评价相关API
   */
  review = {
    /**
     * 添加评价
     * @param {string} type 评价类型 (dish/restaurant)
     * @param {string} itemId 项目ID
     * @param {number} rating 评分
     * @param {string} content 评价内容
     * @param {array} images 图片列表
     * @returns {Promise} 添加结果
     */
    add: async (type, itemId, rating, content = '', images = []) => {
      return await this.callFunction('restaurant', {
        action: 'addReview',
        data: { type, itemId, rating, content, images }
      });
    },
    
    /**
     * 获取评价列表
     * @param {string} type 评价类型 (dish/restaurant)
     * @param {string} itemId 项目ID
     * @param {object} params 其他参数
     * @returns {Promise} 评价列表
     */
    getList: async (type, itemId, params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'getReviews',
        data: { type, itemId, ...params }
      });
    }
  };
  
  /**
   * 浏览历史相关API
   */
  history = {
    /**
     * 记录浏览历史
     * @param {string} type 浏览类型 (dish/restaurant)
     * @param {string} itemId 项目ID
     * @returns {Promise} 记录结果
     */
    record: async (type, itemId) => {
      return await this.callFunction('restaurant', {
        action: 'recordBrowseHistory',
        data: { type, itemId }
      });
    },
    
    /**
     * 获取浏览历史
     * @param {string} type 浏览类型 (dish/restaurant)
     * @param {object} params 其他参数
     * @returns {Promise} 浏览历史
     */
    getList: async (type, params = {}) => {
      return await this.callFunction('restaurant', {
        action: 'getBrowseHistory',
        data: { type, ...params }
      });
    }
  };
  
  /**
   * 数据库相关API
   */
  database = {
    /**
     * 初始化数据库
     * @param {string} action 初始化动作
     * @returns {Promise} 初始化结果
     */
    init: async (action = 'fullInit') => {
      return await this.callFunction('init-database', {
        action: action
      });
    }
  };
}

// 创建单例实例
const cloudAPI = new CloudAPI();

/**
 * 获取云开发API实例
 * @returns {CloudAPI} 云开发API实例
 */
function getCloudAPI() {
  return cloudAPI;
}

/**
 * 重新初始化云开发API（环境切换时使用）
 * @returns {CloudAPI} 新的云开发API实例
 */
function reinitCloudAPI() {
  const newCloudAPI = new CloudAPI();
  return newCloudAPI;
}

module.exports = {
  CloudAPI,
  getCloudAPI,
  reinitCloudAPI
};