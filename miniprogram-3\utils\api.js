const app = getApp();

const request = (options) => {
  return new Promise((resolve, reject) => {
    wx.request({
      ...options,
      url: app.globalData.apiBaseUrl + options.url,
      header: {
        ...options.header,
        'Authorization': `Bearer ${app.globalData.token || ''}`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: reject
    });
  });
};

// 使用小程序支持的模块导出语法
module.exports = request;
