/**
 * 用户认证相关工具函数
 * User authentication utility functions
 */

/**
 * 检查用户是否已登录
 * Check if user is logged in
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const app = getApp();
  return app.globalData.isLoggedIn && app.globalData.token && app.globalData.userInfo;
}

/**
 * 获取当前用户信息
 * Get current user info
 * @returns {object|null} 用户信息对象或null
 */
function getCurrentUser() {
  const app = getApp();
  return app.globalData.isLoggedIn ? app.globalData.userInfo : null;
}

/**
 * 获取当前用户token
 * Get current user token
 * @returns {string|null} token或null
 */
function getCurrentToken() {
  const app = getApp();
  return app.globalData.isLoggedIn ? app.globalData.token : null;
}

/**
 * 检查登录状态，如果未登录则提示并跳转到登录页面
 * Check login status, show prompt and navigate to login if not logged in
 * @param {string} action 操作名称，用于提示信息
 * @returns {boolean} 是否已登录
 */
function requireLogin(action = '此操作') {
  if (isLoggedIn()) {
    return true;
  }
  
  // 显示登录提示
  wx.showModal({
    title: '需要登录',
    content: `${action}需要登录后才能使用，是否前往登录？`,
    confirmText: '去登录',
    cancelText: '取消',
    success: function(res) {
      if (res.confirm) {
        // 跳转到登录页面
        wx.navigateTo({
          url: '/pages/phone-login/phone-login'
        });
      }
    }
  });
  
  return false;
}

/**
 * 执行需要登录的操作
 * Execute operation that requires login
 * @param {function} operation 需要执行的操作函数
 * @param {string} actionName 操作名称，用于提示
 */
function executeWithLogin(operation, actionName = '此操作') {
  if (requireLogin(actionName)) {
    operation();
  }
}

/**
 * 保存未登录状态下的操作数据到本地存储
 * Save operation data to local storage when not logged in
 * @param {string} key 存储键名
 * @param {any} data 要存储的数据
 */
function saveLocalData(key, data) {
  try {
    const existingData = wx.getStorageSync(key) || [];
    existingData.push({
      ...data,
      timestamp: Date.now(),
      synced: false // 标记为未同步到服务器
    });
    wx.setStorageSync(key, existingData);
    console.log(`本地数据已保存: ${key}`);
  } catch (error) {
    console.error('保存本地数据失败:', error);
  }
}

/**
 * 获取本地存储的操作数据
 * Get operation data from local storage
 * @param {string} key 存储键名
 * @returns {array} 本地数据数组
 */
function getLocalData(key) {
  try {
    return wx.getStorageSync(key) || [];
  } catch (error) {
    console.error('获取本地数据失败:', error);
    return [];
  }
}

/**
 * 清除本地存储的操作数据
 * Clear operation data from local storage
 * @param {string} key 存储键名
 */
function clearLocalData(key) {
  try {
    wx.removeStorageSync(key);
    console.log(`本地数据已清除: ${key}`);
  } catch (error) {
    console.error('清除本地数据失败:', error);
  }
}

/**
 * 同步本地数据到服务器（登录后调用）
 * Sync local data to server (call after login)
 * @param {string} key 存储键名
 * @param {function} syncFunction 同步函数
 */
function syncLocalDataToServer(key, syncFunction) {
  const localData = getLocalData(key);
  const unsyncedData = localData.filter(item => !item.synced);
  
  if (unsyncedData.length === 0) {
    return Promise.resolve();
  }
  
  return Promise.all(
    unsyncedData.map(item => syncFunction(item))
  ).then(() => {
    // 标记为已同步
    const updatedData = localData.map(item => ({
      ...item,
      synced: true
    }));
    wx.setStorageSync(key, updatedData);
    console.log(`本地数据同步完成: ${key}`);
  }).catch(error => {
    console.error('同步本地数据失败:', error);
  });
}

module.exports = {
  isLoggedIn,
  getCurrentUser,
  getCurrentToken,
  requireLogin,
  executeWithLogin,
  saveLocalData,
  getLocalData,
  clearLocalData,
  syncLocalDataToServer
};