/**
 * 全局数据管理器
 * 统一管理应用的全局数据和状态
 */
class GlobalDataManager {
  constructor() {
    this.data = {
      userInfo: null,
      isLoggedIn: false,
      appVersion: '1.0.0',
      environment: 'development',
      settings: {},
      cache: new Map()
    };
    
    this.observers = new Map();
  }

  /**
   * 获取全局数据
   * @param {string} key 数据键名
   * @returns {*} 数据值
   */
  get(key) {
    return this.data[key];
  }

  /**
   * 设置全局数据
   * @param {string} key 数据键名
   * @param {*} value 数据值
   */
  set(key, value) {
    const oldValue = this.data[key];
    this.data[key] = value;
    
    // 通知观察者
    this.notifyObservers(key, value, oldValue);
  }

  /**
   * 批量设置数据
   * @param {Object} dataObj 数据对象
   */
  setMultiple(dataObj) {
    Object.keys(dataObj).forEach(key => {
      this.set(key, dataObj[key]);
    });
  }

  /**
   * 添加数据观察者
   * @param {string} key 观察的数据键名
   * @param {Function} callback 回调函数
   */
  observe(key, callback) {
    if (!this.observers.has(key)) {
      this.observers.set(key, []);
    }
    this.observers.get(key).push(callback);
  }

  /**
   * 移除数据观察者
   * @param {string} key 数据键名
   * @param {Function} callback 回调函数
   */
  unobserve(key, callback) {
    if (this.observers.has(key)) {
      const callbacks = this.observers.get(key);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 通知观察者
   * @param {string} key 数据键名
   * @param {*} newValue 新值
   * @param {*} oldValue 旧值
   */
  notifyObservers(key, newValue, oldValue) {
    if (this.observers.has(key)) {
      this.observers.get(key).forEach(callback => {
        try {
          callback(newValue, oldValue, key);
        } catch (error) {
          console.error('数据观察者回调错误 [' + key + ']:', error);
        }
      });
    }
  }

  /**
   * 缓存数据
   * @param {string} key 缓存键名
   * @param {*} value 缓存值
   * @param {number} ttl 过期时间（毫秒）
   */
  cache(key, value, ttl = 0) {
    const cacheItem = {
      value,
      timestamp: Date.now(),
      ttl
    };
    this.data.cache.set(key, cacheItem);
  }

  /**
   * 获取缓存数据
   * @param {string} key 缓存键名
   * @returns {*} 缓存值或null
   */
  getCache(key) {
    const cacheItem = this.data.cache.get(key);
    if (!cacheItem) {
      return null;
    }
    
    // 检查是否过期
    if (cacheItem.ttl > 0 && Date.now() - cacheItem.timestamp > cacheItem.ttl) {
      this.data.cache.delete(key);
      return null;
    }
    
    return cacheItem.value;
  }

  /**
   * 清除缓存
   * @param {string} key 缓存键名，不传则清除所有
   */
  clearCache(key) {
    if (key) {
      this.data.cache.delete(key);
    } else {
      this.data.cache.clear();
    }
  }

  /**
   * 获取所有数据
   * @returns {Object} 全局数据对象
   */
  getAll() {
    return { ...this.data };
  }

  /**
   * 重置数据
   */
  reset() {
    this.data = {
      userInfo: null,
      isLoggedIn: false,
      appVersion: '1.0.0',
      environment: 'development',
      settings: {},
      cache: new Map()
    };
    this.observers.clear();
  }
}

// 导出全局数据管理器
module.exports = GlobalDataManager;
