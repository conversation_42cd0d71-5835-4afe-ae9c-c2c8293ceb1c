// pages/browse-history/browse-history.js
// 浏览历史页面

const app = getApp();

// 直接定义 mock 数据，避免模块导入问题
const mockHistory = [
  { id: 1, name: '麻辣香锅', image: '/images/dish1.jpg', time: '2024-07-20 18:30' },
  { id: 2, name: '日式拉面', image: '/images/dish2.jpg', time: '2024-07-19 12:15' },
  { id: 3, name: '韩式烤肉', image: '/images/dish3.jpg', time: '2024-07-18 20:00' },
  { id: 4, name: '广式早茶', image: '/images/dish4.jpg', time: '2024-07-17 10:00' }
];

Page({
  data: {
    history: []
  },

  onShow: function () {
    this.loadHistory();
  },

  loadHistory: function () {
    this.setData({ history: mockHistory });
  },

  /**
   * 生成模拟浏览历史数据
   */
  generateMockHistory() {
    const types = ['food', 'post', 'user'];
    const foods = ['宫保鸡丁', '麻婆豆腐', '红烧肉', '糖醋里脊', '鱼香肉丝', '回锅肉', '水煮鱼', '口水鸡'];
    const users = ['美食达人小王', '厨艺高手小李', '吃货小张', '料理专家小陈', '烘焙师小刘'];
    
    const history = [];
    
    for (let i = 1; i <= 100; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const viewTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      
      let item = {
        id: i,
        type: type,
        viewTime: viewTime.toISOString(),
        viewCount: Math.floor(Math.random() * 10) + 1,
        duration: Math.floor(Math.random() * 300) + 30 // 浏览时长（秒）
      };
      
      switch (type) {
        case 'food':
          item = {
            ...item,
            title: foods[Math.floor(Math.random() * foods.length)],
            description: '这道菜色香味俱全，制作简单，营养丰富。',
            image: `/images/dish${(i % 4) + 1}.jpg`,
            tags: ['家常菜', '下饭菜', '简单易做'],
            rating: (4 + Math.random()).toFixed(1),
            cookTime: Math.floor(Math.random() * 60 + 15) + '分钟'
          };
          break;
          
        case 'post':
          item = {
            ...item,
            title: `今天做了${foods[Math.floor(Math.random() * foods.length)]}`,
            content: '分享一下今天的美食制作过程，味道真的很棒！',
            images: [`/images/post1.jpg`, `/images/feed${(i % 2) + 1}.png`],
            author: {
              name: users[Math.floor(Math.random() * users.length)],
              avatar: `/images/avatar${(i % 2) + 1}.png`
            },
            likes: Math.floor(Math.random() * 500),
            comments: Math.floor(Math.random() * 100)
          };
          break;
          
        case 'user':
          item = {
            ...item,
            name: users[Math.floor(Math.random() * users.length)],
            avatar: `/images/avatar${(i % 2) + 1}.png`,
            bio: '热爱美食，喜欢分享各种美味佳肴的制作方法。',
            followers: Math.floor(Math.random() * 10000),
            posts: Math.floor(Math.random() * 200),
            verified: Math.random() > 0.7
          };
          break;
      }
      
      history.push(item);
    }
    
    return history.sort((a, b) => new Date(b.viewTime) - new Date(a.viewTime));
  },

  /**
   * 按日期分组历史记录
   */
  groupHistoryByDate(history) {
    const groups = {};
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    
    history.forEach(item => {
      const viewDate = new Date(item.viewTime);
      let dateKey;
      
      if (this.isSameDay(viewDate, today)) {
        dateKey = '今天';
      } else if (this.isSameDay(viewDate, yesterday)) {
        dateKey = '昨天';
      } else {
        dateKey = this.formatDate(viewDate);
      }
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(item);
    });
    
    // 转换为数组格式
    const groupedArray = Object.keys(groups).map(date => ({
      date: date,
      items: groups[date]
    }));
    
    // 按日期排序
    return groupedArray.sort((a, b) => {
      if (a.date === '今天') return -1;
      if (b.date === '今天') return 1;
      if (a.date === '昨天') return -1;
      if (b.date === '昨天') return 1;
      return new Date(b.date) - new Date(a.date);
    });
  },

  /**
   * 判断是否为同一天
   */
  isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
  },

  /**
   * 计算今日浏览数量
   */
  calculateTodayCount() {
    const today = new Date();
    const todayCount = this.data.historyList.filter(item => {
      const viewDate = new Date(item.viewTime);
      return this.isSameDay(viewDate, today);
    }).length;
    
    this.setData({ todayCount });
  },

  /**
   * 加载统计信息
   */
  loadStatistics() {
    // 模拟统计数据
    const stats = {
      totalViews: Math.floor(Math.random() * 1000) + 500,
      todayViews: Math.floor(Math.random() * 50) + 10,
      weekViews: Math.floor(Math.random() * 200) + 100,
      mostViewedType: 'food'
    };
    
    this.setData({ stats });
  },

  /**
   * 刷新历史记录
   */
  refreshHistory() {
    this.setData({ refreshing: true });
    
    setTimeout(() => {
      this.loadBrowseHistory();
      this.loadStatistics();
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    
    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.filterHistory();
    }, 300);
  },

  /**
   * 切换搜索框显示
   */
  onToggleSearch() {
    const searchVisible = !this.data.searchVisible;
    this.setData({ 
      searchVisible,
      searchKeyword: searchVisible ? this.data.searchKeyword : ''
    });
    
    if (!searchVisible) {
      this.filterHistory();
    }
  },

  /**
   * 过滤历史记录
   */
  filterHistory() {
    const { historyList, searchKeyword, filterType } = this.data;
    let filtered = [...historyList];
    
    // 按类型过滤
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.type === filterType);
    }
    
    // 按搜索关键词过滤
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(item => {
        const searchText = (item.title || item.name || item.content || '').toLowerCase();
        return searchText.includes(keyword);
      });
    }
    
    const groupedHistory = this.groupHistoryByDate(filtered);
    this.setData({ groupedHistory });
  },

  /**
   * 设置过滤类型
   */
  onSetFilter() {
    const options = [
      { value: 'all', label: '全部类型' },
      { value: 'food', label: '美食' },
      { value: 'post', label: '动态' },
      { value: 'user', label: '用户' }
    ];
    
    wx.showActionSheet({
      itemList: options.map(item => item.label),
      success: (res) => {
        const selected = options[res.tapIndex];
        this.setData({ filterType: selected.value });
        this.filterHistory();
      }
    });
  },

  /**
   * 切换编辑模式
   */
  onToggleEdit() {
    const editMode = !this.data.editMode;
    this.setData({ 
      editMode,
      selectedItems: []
    });
  },

  /**
   * 选择历史项
   */
  onSelectItem(e) {
    if (!this.data.editMode) return;
    
    const { id } = e.currentTarget.dataset;
    const { selectedItems } = this.data;
    
    const index = selectedItems.indexOf(id);
    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(id);
    }
    
    this.setData({ selectedItems });
  },

  /**
   * 全选/取消全选
   */
  onSelectAll() {
    const allIds = [];
    this.data.groupedHistory.forEach(group => {
      group.items.forEach(item => {
        allIds.push(item.id);
      });
    });
    
    const { selectedItems } = this.data;
    
    if (selectedItems.length === allIds.length) {
      // 取消全选
      this.setData({ selectedItems: [] });
    } else {
      // 全选
      this.setData({ selectedItems: allIds });
    }
  },

  /**
   * 批量删除历史记录
   */
  onBatchDelete() {
    const { selectedItems } = this.data;
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的项目',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '删除浏览记录',
      content: `确定要删除选中的 ${selectedItems.length} 条记录吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteHistoryItems(selectedItems);
        }
      }
    });
  },

  /**
   * 清空所有历史记录
   */
  onClearAll() {
    if (this.data.historyList.length === 0) {
      wx.showToast({
        title: '没有浏览记录',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '清空浏览记录',
      content: '确定要清空所有浏览记录吗？此操作不可恢复。',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.clearAllHistory();
        }
      }
    });
  },

  /**
   * 删除历史记录项
   */
  deleteHistoryItems(ids) {
    wx.showLoading({ title: '删除中...' });
    
    setTimeout(() => {
      const historyList = this.data.historyList.filter(item => !ids.includes(item.id));
      const groupedHistory = this.groupHistoryByDate(historyList);
      
      this.setData({
        historyList,
        groupedHistory,
        selectedItems: [],
        editMode: false,
        totalCount: historyList.length
      });
      
      this.calculateTodayCount();
      
      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 清空所有历史记录
   */
  clearAllHistory() {
    wx.showLoading({ title: '清空中...' });
    
    setTimeout(() => {
      this.setData({
        historyList: [],
        groupedHistory: [],
        selectedItems: [],
        editMode: false,
        totalCount: 0,
        todayCount: 0
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '已清空所有记录',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 点击历史项
   */
  onHistoryItemTap(e) {
    if (this.data.editMode) {
      this.onSelectItem(e);
      return;
    }
    
    const { item } = e.currentTarget.dataset;
    
    // 根据类型跳转到对应页面
    switch (item.type) {
      case 'food':
        wx.showToast({
          title: '跳转到美食详情',
          icon: 'none'
        });
        break;
      case 'post':
        wx.showToast({
          title: '跳转到动态详情',
          icon: 'none'
        });
        break;
      case 'user':
        wx.showToast({
          title: '跳转到用户主页',
          icon: 'none'
        });
        break;
    }
  },

  /**
   * 删除单个历史记录
   */
  onDeleteItem(e) {
    e.stopPropagation();
    const { id } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '删除记录',
      content: '确定要删除这条浏览记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteHistoryItems([id]);
        }
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const time = new Date(timeStr);
    return time.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  /**
   * 格式化浏览时长
   */
  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  }
});