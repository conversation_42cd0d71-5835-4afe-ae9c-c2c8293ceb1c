// pages/community/community.js
// 社区页面 - Community Page

// 引入认证工具函数
const { isLoggedIn, requireLogin } = require('../../utils/auth.js');

// 直接定义 mock 数据，避免模块导入问题
const mockFeedList = [
  {
    postId: 1,
    user: { userName: '美食家小红', avatarUrl: '/images/avatar1.png' },
    time: '2小时前',
    content: '今天发现一家超赞的日料店，刺身新鲜，天妇罗酥脆，幸福感满满！🍣 #今日美食 #网红打卡',
    imageUrl: '/images/post1.jpg',
    location: { storeName: '樱花日料', address: '朝阳区三里屯' },
    shares: 12,
    comments: 34,
    likes: 102,
    isLiked: false,
    collects: 25,
    isCollected: false
  },
  {
    postId: 2,
    user: { userName: '火锅爱好者', avatarUrl: '/images/avatar2.png' },
    time: '昨天',
    content: '没有什么是一顿火锅解决不了的！如果有，那就两顿！🌶️🌶️🌶️ #深夜食堂',
    imageUrl: '/images/hotpot.jpg',
    location: { storeName: '海底捞火锅', address: '海淀区中关村' },
    shares: 5,
    comments: 21,
    likes: 88,
    isLiked: true,
    collects: 18,
    isCollected: true
  },
  {
    postId: 3,
    user: { userName: '健康达人', avatarUrl: '/images/avatar.png' },
    time: '3小时前',
    content: '自制减脂沙拉，颜值和营养并存！🥗 坚持健康饮食第30天 #减脂餐 #家常菜',
    imageUrl: '/images/dish2.jpg',
    location: { storeName: '家里', address: '自制美食' },
    shares: 8,
    comments: 15,
    likes: 67,
    isLiked: false,
    collects: 12,
    isCollected: false
  },
  {
    postId: 4,
    user: { userName: '烘焙小能手', avatarUrl: '/images/default-avatar.png' },
    time: '5小时前',
    content: '周末在家做的芝士蛋糕，奶香浓郁，入口即化～ 🍰 #家常菜 #今日美食',
    imageUrl: '/images/dish3.jpg',
    location: { storeName: '温馨小厨', address: '家庭制作' },
    shares: 15,
    comments: 28,
    likes: 156,
    isLiked: true,
    collects: 42,
    isCollected: false
  },
  {
    postId: 5,
    user: { userName: '街头美食探索者', avatarUrl: '/images/avatar1.png' },
    time: '1天前',
    content: '发现了一家隐藏在胡同里的老字号，手工拉面真的绝了！ 🍜 #网红打卡',
    imageUrl: '/images/dish4.jpg',
    location: { storeName: '老北京面馆', address: '东城区南锣鼓巷' },
    shares: 22,
    comments: 45,
    likes: 203,
    isLiked: false,
    collects: 38,
    isCollected: true
  }
];

Page({
  data: {
    feedList: [],
    // 页面状态控制 - Page state control
    isLoading: false,
    hasError: false,
    errorMessage: '',
    // 外卖商铺数据
    shopList: [
      {
        shopId: 1,
        name: '川味小厨',
        coverImage: '/images/dish1.jpg',
        rating: 4.8,
        distance: '500m',
        description: '正宗川菜，麻辣鲜香'
      },
      {
        shopId: 2,
        name: '日式料理',
        coverImage: '/images/japanese.jpg',
        rating: 4.9,
        distance: '800m',
        description: '新鲜刺身，精致寿司'
      },
      {
        shopId: 3,
        name: '韩式烤肉',
        coverImage: '/images/korean.jpg',
        rating: 4.7,
        distance: '1.2km',
        description: '优质牛肉，地道韩味'
      },
      {
        shopId: 4,
        name: '粤式茶餐厅',
        coverImage: '/images/cantonese.jpg',
        rating: 4.6,
        distance: '600m',
        description: '港式点心，经典粤菜'
      }
    ],
    // 分类导航数据
    categoryList: [
      { id: 'hot', name: '热门', icon: '/images/svg/heart.svg' },
      { id: 'new', name: '最新', icon: '/images/svg/check.svg' },
      { id: 'nearby', name: '附近', icon: '/images/icon_location.svg' },
      { id: 'follow', name: '关注', icon: '/images/svg/heart.svg' }
    ],
    // 热门话题数据
    topicList: [
      { id: 1, name: '今日美食', count: 1234 },
      { id: 2, name: '网红打卡', count: 856 },
      { id: 3, name: '家常菜', count: 642 },
      { id: 4, name: '减脂餐', count: 423 },
      { id: 5, name: '深夜食堂', count: 389 }
    ]
  },

  onLoad: function (options) {
    this.loadFeedData();
  },

  /**
   * 加载动态数据 - Load feed data
   */
  loadFeedData: function() {
    this.setData({
      isLoading: true,
      hasError: false,
      errorMessage: ''
    });

    try {
      // 模拟网络请求延迟 - Simulate network request delay
      setTimeout(() => {
        this.refreshFeedData();
        this.setData({
          isLoading: false
        });
      }, 500);
    } catch (error) {
      this.handleError('加载数据失败', error);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 页面显示时刷新数据 - Refresh data when page shows
    this.refreshFeedData();
  },

  /**
   * 刷新动态数据 - Refresh feed data
   */
  onRefresh: function() {
    console.log('社区页面收到刷新通知');
    this.refreshFeedData();
  },

  /**
   * 刷新动态数据的具体实现 - Implementation of refresh feed data
   */
  refreshFeedData: function() {
    // 获取用户发布的内容 - Get user published posts
    const userPosts = this.getUserPosts();
    
    // 处理用户发布内容的时间显示 - Process time display for user posts
    const processedUserPosts = userPosts.map(post => {
      return {
        ...post,
        time: this.formatTime(post.createTime) // 格式化时间显示
      };
    });
    
    // 合并用户发布的内容和模拟数据 - Merge user posts with mock data
    const combinedFeedList = [...processedUserPosts, ...mockFeedList];
    
    // 按时间排序，最新的在前面 - Sort by time, newest first
    combinedFeedList.sort((a, b) => {
      const timeA = a.createTime || Date.now();
      const timeB = b.createTime || Date.now();
      return timeB - timeA;
    });
    
    // 重新加载动态数据 - Reload feed data
    this.setData({ 
      feedList: combinedFeedList 
    });
    
    // 显示刷新提示 - Show refresh toast
    if (userPosts.length > 0) {
      wx.showToast({
        title: `发现${userPosts.length}条新动态`,
        icon: 'success',
        duration: 1000
      });
    }
  },

  /**
   * 获取用户发布的内容 - Get user published posts
   */
  getUserPosts: function() {
    try {
      const userPosts = wx.getStorageSync('userPosts') || [];
      console.log('获取到用户发布的内容:', userPosts.length, '条');
      return userPosts;
    } catch (error) {
      console.error('获取用户发布内容失败:', error);
      return [];
    }
  },

  /**
   * 格式化时间显示 - Format time display
   */
  formatTime: function(timestamp) {
    if (!timestamp) return '刚刚';
    
    const now = new Date().getTime();
    const diff = now - timestamp;
    
    // 小于1分钟显示"刚刚"
    if (diff < 60 * 1000) {
      return '刚刚';
    }
    
    // 小于1小时显示"X分钟前"
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `${minutes}分钟前`;
    }
    
    // 小于24小时显示"X小时前"
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours}小时前`;
    }
    
    // 小于7天显示"X天前"
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `${days}天前`;
    }
    
    // 超过7天显示具体日期
    const date = new Date(timestamp);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
   },

   /**
    * 统一错误处理 - Unified error handling
    */
   handleError: function(message, error) {
     console.error(message, error);
     
     this.setData({
       isLoading: false,
       hasError: true,
       errorMessage: message
     });

     // 显示错误提示 - Show error toast
     wx.showToast({
       title: message,
       icon: 'none',
       duration: 2000
     });
   },

   /**
    * 重试加载数据 - Retry loading data
    */
   onRetry: function() {
     this.loadFeedData();
   },

  /**
   * 点赞功能 - 需要登录后才能操作
   * Like function - requires login to operate
   */
  toggleLike: function(e) {
    // 检查登录状态，未登录则跳转登录页面
    if (!isLoggedIn()) {
      requireLogin('点赞');
      return;
    }

    const index = e.currentTarget.dataset.index;
    const item = this.data.feedList[index];
    const isLiked = !item.isLiked;
    const likes = isLiked ? item.likes + 1 : item.likes - 1;

    // 更新UI状态
    this.setData({
      [`feedList[${index}].isLiked`]: isLiked,
      [`feedList[${index}].likes`]: likes
    });

    // 同步到服务器
    this.syncLikeToServer(item.postId, isLiked);

    // 显示点赞反馈
    wx.showToast({
      title: isLiked ? '点赞成功' : '取消点赞',
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 同步点赞状态到服务器
   * Sync like status to server
   */
  syncLikeToServer: function(postId, isLiked) {
    // TODO: 实现服务器同步逻辑
    console.log('同步点赞到服务器:', { postId, isLiked });
    
    // 这里应该调用API同步到服务器
    // 示例代码：
    // wx.request({
    //   url: `${app.globalData.apiBaseUrl}/posts/${postId}/like`,
    //   method: isLiked ? 'POST' : 'DELETE',
    //   header: {
    //     'Authorization': `Bearer ${app.globalData.token}`
    //   },
    //   success: (res) => {
    //     console.log('点赞同步成功');
    //   },
    //   fail: (error) => {
    //     console.error('点赞同步失败:', error);
    //   }
    // });
  },

  /**
   * 保存点赞状态到本地存储
   * Save like status to local storage
   */


  /**
   * 评论功能 - Comment function
   */
  /**
   * 评论功能 - 需要登录后才能操作
   * Comment function - requires login to operate
   */
  handleComment: function(e) {
    // 检查登录状态，未登录则跳转登录页面
    if (!isLoggedIn()) {
      requireLogin('评论');
      return;
    }

    const postId = e.currentTarget.dataset.postId;
    wx.showToast({
      title: '评论功能开发中',
      icon: 'none',
      duration: 1500
    });
    // 这里可以跳转到评论页面
    // wx.navigateTo({
    //   url: `/pages/comment/comment?postId=${postId}`
    // });
  },

  /**
   * 分享功能 - Share function
   * 支持未登录状态下的本地记录
   */
  /**
   * 分享功能 - 需要登录后才能操作
   * Share function - requires login to operate
   */
  handleShare: function(e) {
    // 检查登录状态，未登录则跳转登录页面
    if (!isLoggedIn()) {
      requireLogin('分享');
      return;
    }

    const postId = e.currentTarget.dataset.postId;
    const index = e.currentTarget.dataset.index;
    const item = this.data.feedList[index];
    
    console.log('分享动态:', postId);
    
    wx.showActionSheet({
      itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
      success: (res) => {
        console.log('选择分享方式:', res.tapIndex);
        
        // 同步分享数据到服务器
        const shareTypeNames = ['分享到微信', '分享到朋友圈', '复制链接'];
        const shareData = {
          postId: postId,
          shareType: res.tapIndex,
          shareTypeName: shareTypeNames[res.tapIndex],
          action: 'share',
          postData: {
            postId: item.postId,
            user: item.user,
            content: item.content,
            imageUrl: item.imageUrl
          }
        };
        this.syncShareToServer(shareData);
        
        // 更新分享数量
        this.setData({
          [`feedList[${index}].shares`]: (item.shares || 0) + 1
        });
        
        wx.showToast({
          title: '分享成功',
          icon: 'success'
        });
      }
    });
  },



  /**
   * 同步分享数据到服务器
   * Sync share data to server
   */
  syncShareToServer: function(shareData) {
    // TODO: 实现服务器同步逻辑
    console.log('同步分享到服务器:', shareData);
    
    // 这里应该调用API同步到服务器
    // 示例代码：
    // wx.request({
    //   url: `${app.globalData.apiBaseUrl}/posts/${shareData.postId}/share`,
    //   method: 'POST',
    //   data: shareData,
    //   header: {
    //     'Authorization': `Bearer ${app.globalData.token}`
    //   },
    //   success: (res) => {
    //     console.log('分享同步成功');
    //   },
    //   fail: (error) => {
    //     console.error('分享同步失败:', error);
    //   }
    // });
  },

  /**
   * 收藏功能 - Collect function
   */
  /**
   * 收藏功能 - Collect function
   * 支持未登录状态下的本地存储
   */
  /**
   * 收藏功能 - 需要登录后才能操作
   * Collect function - requires login to operate
   */
  toggleCollect: function(e) {
    // 检查登录状态，未登录则跳转登录页面
    if (!isLoggedIn()) {
      requireLogin('收藏');
      return;
    }

    const index = e.currentTarget.dataset.index;
    const item = this.data.feedList[index];
    const isCollected = !item.isCollected;
    const collects = isCollected ? (item.collects || 0) + 1 : (item.collects || 0) - 1;

    // 更新UI状态
    this.setData({
      [`feedList[${index}].isCollected`]: isCollected,
      [`feedList[${index}].collects`]: Math.max(0, collects)
    });

    // 同步到服务器
    this.syncCollectToServer(item.postId, isCollected);

    // 显示收藏反馈
    wx.showToast({
      title: isCollected ? '收藏成功' : '取消收藏',
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 同步收藏状态到服务器
   * Sync collect status to server
   */
  syncCollectToServer: function(postId, isCollected) {
    // TODO: 实现服务器同步逻辑
    console.log('同步收藏到服务器:', { postId, isCollected });
    
    // 这里应该调用API同步到服务器
    // 示例代码：
    // wx.request({
    //   url: `${app.globalData.apiBaseUrl}/posts/${postId}/collect`,
    //   method: isCollected ? 'POST' : 'DELETE',
    //   header: {
    //     'Authorization': `Bearer ${app.globalData.token}`
    //   },
    //   success: (res) => {
    //     console.log('收藏同步成功');
    //   },
    //   fail: (error) => {
    //     console.error('收藏同步失败:', error);
    //   }
    // });
  },



  /**
   * 图片预览功能 - Image Preview Function
   */
  onPreviewImage: function(e) {
    const { images, current } = e.currentTarget.dataset;
    
    wx.previewImage({
      current: current,
      urls: images
    });
  },

  navigateToPublish: function() {
    wx.navigateTo({
      url: '/pages/publish/publish'
    });
  },

  // 外卖商铺相关事件处理
  goToShop: function(e) {
    const shopId = e.currentTarget.dataset.shopId;
    wx.showToast({
      title: `进入商铺 ${shopId}`,
      icon: 'none',
      duration: 1500
    });
    // 这里可以跳转到商铺详情页
    // wx.navigateTo({
    //   url: `/pages/shop-detail/shop-detail?shopId=${shopId}`
    // });
  },

  viewMoreShops: function() {
    wx.showToast({
      title: '查看更多商铺',
      icon: 'none',
      duration: 1500
    });
    // 这里可以跳转到商铺列表页
    // wx.navigateTo({
    //   url: '/pages/shop-list/shop-list'
    // });
  },

  // 分类选择事件处理
  selectCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    wx.showToast({
      title: `选择分类: ${category}`,
      icon: 'none',
      duration: 1500
    });
    // 这里可以根据分类筛选内容
    this.filterFeedByCategory(category);
  },

  // 话题选择事件处理
  selectTopic: function(e) {
    const topicId = e.currentTarget.dataset.topic;
    const topic = this.data.topicList.find(item => item.id === topicId);
    wx.showToast({
      title: `选择话题: #${topic.name}`,
      icon: 'none',
      duration: 1500
    });
    // 这里可以根据话题筛选内容
    this.filterFeedByTopic(topicId);
  },

  // 根据分类筛选动态内容
  filterFeedByCategory: function(category) {
    // 模拟根据分类筛选逻辑
    console.log('筛选分类:', category);
    // 实际项目中可以调用API获取对应分类的内容
  },

  // 根据话题筛选动态内容
  filterFeedByTopic: function(topicId) {
    // 模拟根据话题筛选逻辑
    console.log('筛选话题:', topicId);
    // 实际项目中可以调用API获取对应话题的内容
  }
});
