// pages/publish/publish.js
// 发布页面逻辑 - Publish Page Logic

Page({
  /**
   * 页面的初始数据 - Initial Page Data
   */
  data: {
    // 文本内容 - Text Content
    content: '',
    maxContentLength: 500,
    
    // 图片相关 - Image Related
    selectedImages: [],
    maxImageCount: 9,
    
    // 位置信息 - Location Info
    selectedLocation: null,
    
    // 分类标签 - Category Tags
    categories: [
      { id: 'food', name: '美食' },
      { id: 'drink', name: '饮品' },
      { id: 'snack', name: '小吃' },
      { id: 'fastfood', name: '快餐' },
      { id: 'dessert', name: '甜品' },
      { id: 'hotpot', name: '火锅' },
      { id: 'bbq', name: '烧烤' },
      { id: 'other', name: '其他' }
    ],
    selectedCategory: '',
    
    // 设置选项 - Settings Options
    syncToTimeline: false,
    allowComment: true,
    
    // 状态控制 - State Control
    canPublish: false,
    isPublishing: false,
    
    // 错误处理 - Error handling
    hasError: false,
    errorMessage: ''
  },

  /**
   * 生命周期函数--监听页面加载 - Page Load Lifecycle
   */
  onLoad(options) {
    console.log('发布页面加载');
    this.checkCanPublish();
  },

  /**
   * 生命周期函数--监听页面显示 - Page Show Lifecycle
   */
  onShow() {
    // 页面显示时检查发布条件 - Check publish conditions when page shows
    this.checkCanPublish();
  },

  /**
   * 检查是否可以发布 - Check if can publish
   */
  checkCanPublish() {
    const { content, selectedImages, selectedCategory } = this.data;
    const canPublish = content.trim().length > 0 || selectedImages.length > 0;
    
    this.setData({
      canPublish
    });
  },

  /**
   * 文本内容输入事件 - Text Content Input Event
   */
  onContentInput(e) {
    const content = e.detail.value;
    this.setData({
      content
    }, () => {
      this.checkCanPublish();
    });
  },

  /**
   * 选择图片事件 - Choose Image Event
   */
  onChooseImage() {
    const { selectedImages, maxImageCount } = this.data;
    const remainingCount = maxImageCount - selectedImages.length;
    
    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功:', res);
        const newImages = res.tempFiles.map(file => file.tempFilePath);
        
        this.setData({
          selectedImages: [...selectedImages, ...newImages]
        }, () => {
          this.checkCanPublish();
        });
        
        wx.showToast({
          title: `已添加${newImages.length}张图片`,
          icon: 'success',
          duration: 1500
        });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 预览图片事件 - Preview Image Event
   */
  onPreviewImage(e) {
    const index = e.currentTarget.dataset.index;
    const { selectedImages } = this.data;
    
    wx.previewImage({
      current: selectedImages[index],
      urls: selectedImages
    });
  },

  /**
   * 删除图片事件 - Delete Image Event
   */
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const { selectedImages } = this.data;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          const newImages = selectedImages.filter((_, i) => i !== index);
          this.setData({
            selectedImages: newImages
          }, () => {
            this.checkCanPublish();
          });
          
          wx.showToast({
            title: '图片已删除',
            icon: 'success',
            duration: 1500
          });
        }
      }
    });
  },

  /**
   * 选择位置事件 - Choose Location Event
   */
  /**
   * 选择位置功能 - Choose Location Function
   */
  onChooseLocation() {
    // 先检查位置权限 - Check location permission first
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === false) {
          // 用户之前拒绝了位置权限，引导用户手动开启 - User denied location permission, guide to enable manually
          wx.showModal({
            title: '需要位置权限',
            content: '请在设置中开启位置权限，以便选择发布位置',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      // 权限开启成功，重新调用选择位置 - Permission enabled, retry choose location
                      this.chooseLocationWithPermission();
                    }
                  }
                });
              }
            }
          });
        } else {
          // 有权限或未询问过权限，直接调用选择位置 - Has permission or never asked, directly choose location
          this.chooseLocationWithPermission();
        }
      },
      fail: (error) => {
        console.error('获取设置失败:', error);
        // 获取设置失败，直接尝试选择位置 - Failed to get settings, try choose location directly
        this.chooseLocationWithPermission();
      }
    });
  },

  /**
   * 在有权限的情况下选择位置 - Choose location with permission
   */
  chooseLocationWithPermission() {
    wx.chooseLocation({
      success: (res) => {
        console.log('选择位置成功:', res);
        this.setData({
          selectedLocation: {
            name: res.name || '未知位置',
            address: res.address || '',
            latitude: res.latitude,
            longitude: res.longitude
          }
        });
        
        wx.showToast({
          title: '位置已选择',
          icon: 'success',
          duration: 1500
        });
      },
      fail: (error) => {
        console.error('选择位置失败:', error);
        let errorMsg = '选择位置失败';
        
        if (error.errMsg.includes('auth deny') || error.errMsg.includes('authorize deny')) {
          errorMsg = '需要位置权限才能选择位置';
          wx.showModal({
            title: '位置权限被拒绝',
            content: '请在设置中开启位置权限，以便选择发布位置',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting();
              }
            }
          });
        } else if (error.errMsg.includes('cancel')) {
          errorMsg = '已取消选择位置';
        } else {
          errorMsg = '选择位置失败，请重试';
        }
        
        if (!error.errMsg.includes('cancel')) {
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  /**
   * 分类选择事件 - Category Select Event
   */
  onCategorySelect(e) {
    const category = e.currentTarget.dataset.category;
    const { selectedCategory } = this.data;
    
    // 如果点击的是已选中的分类，则取消选择 - If clicking selected category, deselect it
    const newCategory = selectedCategory === category ? '' : category;
    
    this.setData({
      selectedCategory: newCategory
    });
    
    console.log('选择分类:', newCategory);
  },

  /**
   * 同步到朋友圈开关事件 - Sync to Timeline Switch Event
   */
  onSyncChange(e) {
    this.setData({
      syncToTimeline: e.detail.value
    });
    console.log('同步到朋友圈:', e.detail.value);
  },

  /**
   * 允许评论开关事件 - Allow Comment Switch Event
   */
  onCommentChange(e) {
    this.setData({
      allowComment: e.detail.value
    });
    console.log('允许评论:', e.detail.value);
  },

  /**
   * 取消按钮点击事件 - Cancel Button Tap Event
   */
  onCancelTap() {
    const { content, selectedImages } = this.data;
    
    // 如果有内容，询问是否确认取消 - If has content, ask for confirmation
    if (content.trim().length > 0 || selectedImages.length > 0) {
      wx.showModal({
        title: '确认取消',
        content: '取消后内容将不会保存，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 发布提交事件 - Publish Submit Event
   */
  onPublishSubmit() {
    const { canPublish, isPublishing } = this.data;
    
    if (!canPublish || isPublishing) {
      return;
    }
    
    // 添加触觉反馈 - Add haptic feedback
    wx.vibrateShort();
    
    this.performPublish();
  },

  /**
   * 执行发布操作 - Perform Publish Operation
   */
  performPublish() {
    const { content, selectedImages, selectedLocation, selectedCategory, syncToTimeline, allowComment } = this.data;

    // 显示发布中状态 - Show publishing state
    this.setData({
      isPublishing: true
    });

    // 构建发布数据 - Build publish data
    const publishData = {
      postId: Date.now(), // 使用时间戳作为唯一postId - Use timestamp as unique postId
      content: content.trim(),
      images: selectedImages.length > 0 ? selectedImages : [], // 确保images字段始终为数组 - Ensure images field is always an array
      location: selectedLocation,
      category: selectedCategory,
      syncToTimeline,
      allowComment,
      createTime: new Date().getTime(), // 创建时间戳 - Creation timestamp
      time: '刚刚', // 初始显示时间文本 - Initial display time text
      // 用户信息 - User info
      user: {
        userName: '我', // 社区页面使用的用户名字段 - Username field used by community page
        avatarUrl: '/images/default-avatar.png' // 社区页面使用的头像字段 - Avatar field used by community page
      },
      // 初始化互动数据 - Initialize interaction data
      likes: 0,
      comments: 0,
      shares: 0,
      isLiked: false
    };

    // 如果有位置信息，确保格式兼容 - Ensure location format compatibility if location exists
    if (publishData.location) {
      publishData.location.storeName = publishData.location.name || publishData.location.storeName;
    }

    console.log('发布数据:', publishData);

    // 将新发布的内容保存到全局数据 - Save new post to global data
    this.saveNewPost(publishData);

    // 模拟发布过程 - Simulate publish process
    setTimeout(() => {
      // 模拟网络请求可能失败的情况 - Simulate possible network failure
      const isSuccess = Math.random() > 0.1; // 90%成功率 - 90% success rate
      
      if (isSuccess) {
        this.handlePublishSuccess(publishData);
      } else {
        this.handlePublishFailure(new Error('网络连接失败'));
      }
    }, 1500);
  },

  /**
   * 保存新发布的内容到全局数据 - Save new post to global data
   */
  saveNewPost(publishData) {
    try {
      // 获取当前存储的发布数据 - Get current stored posts
      let storedPosts = wx.getStorageSync('userPosts') || [];
      
      // 将新发布的内容添加到开头 - Add new post to the beginning
      storedPosts.unshift(publishData);
      
      // 限制存储的发布数量，避免占用过多存储空间 - Limit stored posts to avoid excessive storage
      if (storedPosts.length > 50) {
        storedPosts = storedPosts.slice(0, 50);
      }
      
      // 保存到本地存储 - Save to local storage
      wx.setStorageSync('userPosts', storedPosts);
      
      console.log('新发布内容已保存到本地存储');
    } catch (error) {
      console.error('保存发布内容失败:', error);
    }
  },

  /**
   * 处理发布成功 - Handle Publish Success
   */
  handlePublishSuccess(publishData) {
    // 重置发布状态 - Reset publishing state
    this.setData({
      isPublishing: false
    });

    // 清空表单数据 - Clear form data
    this.resetForm();

    wx.showToast({
      title: '发布成功',
      icon: 'success',
      duration: 1500
    });

    // 延迟返回上一页 - Delay navigate back
    setTimeout(() => {
      wx.navigateBack({
        success: () => {
          // 通知上一页刷新数据 - Notify previous page to refresh data
          const pages = getCurrentPages();
          if (pages.length > 1) {
            const prevPage = pages[pages.length - 2];
            if (prevPage.onRefresh) {
              prevPage.onRefresh();
            } else if (prevPage.refreshFeedData) {
              // 兼容直接调用刷新方法 - Compatible with direct refresh method call
              prevPage.refreshFeedData();
            }
          }
        },
        fail: (error) => {
          console.error('返回上一页失败:', error);
          // 如果返回失败，尝试跳转到社区页面 - If navigate back fails, try to navigate to community page
          wx.switchTab({
            url: '/pages/community/community'
          });
        }
      });
    }, 800);
  },

  /**
   * 重置表单数据 - Reset form data
   */
  resetForm() {
    this.setData({
      content: '',
      selectedImages: [],
      selectedLocation: null,
      selectedCategory: '',
      syncToTimeline: false,
      allowComment: true,
      canPublish: false
    });
    console.log('表单数据已重置');
  },

  /**
   * 处理发布失败 - Handle Publish Failure
   */
  handlePublishFailure(error) {
    console.error('发布失败:', error);
    
    // 重置发布状态并设置错误信息 - Reset publishing state and set error info
    this.setData({
      isPublishing: false,
      hasError: true,
      errorMessage: '发布失败，请检查网络连接后重试'
    });

    wx.showToast({
      title: '发布失败，请重试',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 清除错误状态 - Clear error state
   */
  clearError: function() {
    this.setData({
      hasError: false,
      errorMessage: ''
    });
  },

  /**
   * 重试发布 - Retry publish
   */
  onRetryPublish: function() {
    this.clearError();
    this.onPublishSubmit();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作 - Pull Down Refresh
   */
  onPullDownRefresh() {
    // 发布页面不需要下拉刷新 - Publish page doesn't need pull down refresh
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享 - User Share App Message
   */
  onShareAppMessage() {
    return {
      title: '分享你的美食体验',
      path: '/pages/publish/publish',
      imageUrl: '/images/share-cover.png'
    };
  }
});
