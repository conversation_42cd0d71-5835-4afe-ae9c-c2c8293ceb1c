<!--pages/phone-login/phone-login.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title">手机号登录</view>
    <view class="placeholder"></view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo">📱</view>
      <view class="welcome-text">手机号快速登录</view>
      <view class="subtitle">验证后即可登录/注册</view>
    </view>

    <!-- 手机号输入 -->
    <view class="input-group">
      <view class="input-label">手机号</view>
      <view class="input-wrapper">
        <input 
          class="phone-input"
          type="number"
          placeholder="请输入手机号"
          value="{{phone}}"
          bindinput="onPhoneInput"
          maxlength="11"
        />
      </view>
    </view>

    <!-- 验证码输入 -->
    <view class="input-group">
      <view class="input-label">验证码</view>
      <view class="input-wrapper code-wrapper">
        <input 
          class="code-input"
          type="number"
          placeholder="请输入验证码"
          value="{{code}}"
          bindinput="onCodeInput"
          maxlength="6"
        />
        <button 
          class="code-btn {{canSendCode ? 'active' : 'disabled'}}"
          bindtap="sendCode"
          disabled="{{!canSendCode}}"
        >
          {{codeButtonText}}
        </button>
      </view>
    </view>

    <!-- 登录按钮 -->
    <button 
      class="login-btn {{phone && code ? 'active' : 'disabled'}}"
      bindtap="handleLogin"
      disabled="{{!phone || !code || isLoading}}"
      loading="{{isLoading}}"
    >
      {{isLoading ? '登录中...' : '登录'}}
    </button>

    <!-- 提示信息 -->
    <view class="tips">
      <text class="tip-text">登录即表示同意</text>
      <text class="link">《用户协议》</text>
      <text class="tip-text">和</text>
      <text class="link">《隐私政策》</text>
    </view>
  </view>

  <!-- 其他登录方式 -->
  <view class="other-login">
    <view class="divider">
      <view class="line"></view>
      <text class="divider-text">其他登录方式</text>
      <view class="line"></view>
    </view>
    
    <view class="login-methods">
      <view class="method-item" bindtap="handleWechatLogin">
        <view class="method-icon wechat">微</view>
        <text class="method-text">微信登录</text>
      </view>
    </view>
  </view>
</view>
