<!--pages/my-posts/my-posts.wxml-->
<!-- 我的动态页面 - 黑白极简设计 -->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="onBack">
      <text class="nav-back-icon">←</text>
    </view>
    <view class="nav-title">我的动态</view>
    <view class="nav-action" bindtap="onCreatePost">
      <text class="nav-create">+</text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-container">
    <view class="tabs">
      <view wx:for="{{tabs}}" 
            wx:key="key" 
            class="tab-item {{activeTab === item.key ? 'active' : ''}}"
            bindtap="onTabChange"
            data-tab="{{item.key}}">
        <text class="tab-label">{{item.label}}</text>
        <text class="tab-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
      </view>
    </view>
  </view>

  <!-- 动态列表 -->
  <view class="posts-container">
    <!-- 空状态 -->
    <view wx:if="{{posts.length === 0 && !loading}}" class="empty-state">
      <view class="empty-icon">📝</view>
      <view class="empty-title">暂无动态</view>
      <view class="empty-desc">快去发布你的第一条美食动态吧！</view>
      <button class="empty-action" bindtap="onCreatePost">发布动态</button>
    </view>

    <!-- 动态列表 -->
    <view wx:for="{{posts}}" wx:key="id" class="post-item">
      <!-- 动态头部 -->
      <view class="post-header">
        <view class="post-status">
          <text class="status-dot {{item.status}}"></text>
          <text class="status-text">{{item.status === 'published' ? '已发布' : '草稿'}}</text>
        </view>
        <view class="post-time">{{formatTime(item.createTime)}}</view>
        <view class="post-actions" bindtap="onShowPostActions" data-id="{{item.id}}">
          <text class="action-icon">⋯</text>
        </view>
      </view>

      <!-- 动态内容 -->
      <view class="post-content" bindtap="onViewPost" data-id="{{item.id}}">
        <view class="post-title">{{item.title}}</view>
        <view class="post-text">{{item.content}}</view>
        
        <!-- 图片 -->
        <view wx:if="{{item.images && item.images.length > 0}}" class="post-images">
          <image wx:for="{{item.images}}" 
                 wx:for-item="img" 
                 wx:key="*this"
                 class="post-image"
                 src="{{img}}"
                 mode="aspectFill"/>
        </view>
      </view>

      <!-- 动态统计 -->
      <view class="post-stats">
        <view class="stat-item">
          <text class="stat-icon">👁</text>
          <text class="stat-count">{{item.viewCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">❤️</text>
          <text class="stat-count">{{item.likeCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">💬</text>
          <text class="stat-count">{{item.commentCount}}</text>
        </view>
        <view class="post-edit" bindtap="onEditPost" data-id="{{item.id}}">
          <text class="edit-text">编辑</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-more">
      <text class="loading-text">加载中...</text>
    </view>
    
    <view wx:if="{{!hasMore && posts.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多了</text>
    </view>
  </view>

  <!-- 操作菜单 -->
  <view wx:if="{{showActionSheet}}" class="action-sheet-mask" bindtap="onHideActionSheet">
    <view class="action-sheet" catchtap="">
      <view class="action-sheet-header">
        <view class="action-title">选择操作</view>
        <view class="action-close" bindtap="onHideActionSheet">×</view>
      </view>
      <view class="action-list">
        <view class="action-item" bindtap="onEditPost" data-id="{{selectedPost.id}}">
          <text class="action-icon">✏️</text>
          <text class="action-text">编辑</text>
        </view>
        <view class="action-item" bindtap="onSharePost">
          <text class="action-icon">📤</text>
          <text class="action-text">分享</text>
        </view>
        <view class="action-item danger" bindtap="onDeletePost">
          <text class="action-icon">🗑️</text>
          <text class="action-text">删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>