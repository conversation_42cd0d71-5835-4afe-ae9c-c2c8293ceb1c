// app.js
// [RECONSTRUCTED] Backend-driven login and data management.

// 引入环境配置管理
const { initializeEnvironment, getEnvironmentConfig } = require('./config/environment.js');

App({
  globalData: {
    userInfo: null,
    token: null,
    isLoggedIn: false,
    apiBaseUrl: '', // 将由环境配置动态设置
    environment: null, // 当前环境配置
    
    // 云开发相关状态
    cloudInitialized: false, // 云开发是否已初始化
    useCloudFunction: false, // 是否使用云函数

    // Frontend data will be removed as they will be fetched from backend
    preferences: {
      likes: [],
      dislikes: [],
      canEat: [],
      cannotEat: []
    },
    currentResult: null, // 存储当前选中的完整食物对象
    lastResult: null, // 添加lastResult字段，用于存储最后选择的食物名称
    shareHistory: []
  },

  // [REFACTOR] Add a callback queue for login state
  loginReadyCallback: [],

  onLaunch: function () {
    // 初始化环境配置
    this.initializeEnvironment();

    // 清除所有本地登录状态，确保每次启动都是未登录状态
    this.clearAllLoginData();

    // 加载用户偏好设置（不包含登录信息）
    this.loadUserPreferences();

    console.log('小程序启动完成，当前为未登录状态');
  },

  /**
   * 清除所有登录相关数据
   * 确保小程序启动时为未登录状态
   */
  clearAllLoginData: function() {
    try {
      // 重置全局登录状态
      this.globalData.userInfo = null;
      this.globalData.token = null;
      this.globalData.isLoggedIn = false;

      // 清除本地存储的登录信息
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');

      // 清除任何可能残留的开发工具标志
      wx.removeStorageSync('dev_reset_login_on_launch');

      console.log('已清除所有登录数据，当前为未登录状态');
    } catch (error) {
      console.error('清除登录数据失败:', error);
      // 即使出错也要确保基本的登录状态被重置
      this.globalData.userInfo = null;
      this.globalData.token = null;
      this.globalData.isLoggedIn = false;
    }
  },

  /**
   * 初始化环境配置
   */
  initializeEnvironment: function() {
    try {
      const envConfig = initializeEnvironment();
      this.globalData.environment = envConfig;
      this.globalData.apiBaseUrl = envConfig.apiBaseUrl;
      this.globalData.useCloudFunction = envConfig.useCloudFunction || false;
      
      console.log(`环境初始化完成: ${envConfig.name}`);
      console.log(`API基础地址: ${envConfig.apiBaseUrl}`);
      console.log(`使用云函数: ${this.globalData.useCloudFunction}`);
      
      // 初始化云开发（如果启用）
      if (envConfig.useCloudFunction && envConfig.cloudEnvId) {
        this.initializeCloudDevelopment(envConfig.cloudEnvId);
      }
    } catch (error) {
      console.error('环境初始化失败:', error);
      // 使用默认配置
      this.globalData.apiBaseUrl = 'http://localhost:3000/api/v1';
    }
  },

  /**
   * 初始化云开发
   * @param {string} envId 云开发环境ID
   */
  initializeCloudDevelopment: function(envId) {
    try {
      // 检查云开发是否可用
      if (!wx.cloud) {
        console.warn('当前环境不支持云开发');
        this.globalData.cloudInitialized = false;
        this.globalData.useCloudFunction = false;
        return;
      }

      wx.cloud.init({
        env: envId,
        traceUser: true
      });
      
      console.log(`云开发初始化完成: ${envId}`);
      this.globalData.cloudInitialized = true;
      
      // 测试云开发连接
      this.testCloudConnection();
    } catch (error) {
      console.error('云开发初始化失败:', error);
      this.globalData.cloudInitialized = false;
      this.globalData.useCloudFunction = false;
    }
  },

  /**
   * 测试云开发连接
   */
  testCloudConnection: function() {
    try {
      // 使用auth云函数进行连接测试
      wx.cloud.callFunction({
        name: 'auth',
        data: { action: 'ping' },
        timeout: 5000,
        success: (res) => {
          console.log('[云开发] 连接测试成功:', res);

          // 初始化数据库（仅在开发环境）
          const envConfig = this.getEnvironmentConfig();
          if (envConfig && envConfig.debug) {
            this.initCloudDatabase();
          }
        },
        fail: (error) => {
          console.warn('[云开发] 连接测试失败，可能影响云函数调用:', error);
          // 不禁用云开发，但记录警告
        }
      });
    } catch (error) {
      console.warn('[云开发] 连接测试异常:', error);
    }
  },

  /**
   * 初始化云开发数据库
   */
  initCloudDatabase: function() {
    console.log('[云开发] 开始初始化数据库...');

    wx.cloud.callFunction({
      name: 'init-db',
      data: {},
      timeout: 30000, // 30秒超时
      success: (res) => {
        if (res.result && res.result.success) {
          console.log('[云开发] 数据库初始化成功:', res.result);
        } else {
          console.warn('[云开发] 数据库初始化失败:', res.result);
        }
      },
      fail: (error) => {
        console.warn('[云开发] 数据库初始化调用失败:', error);
        // 数据库初始化失败不影响正常使用
      }
    });
  },

  /**
   * 获取当前环境配置
   */
  getEnvironmentConfig: function() {
    return this.globalData.environment || getEnvironmentConfig();
  },

  /**
   * 切换环境（仅开发调试使用）
   */
  switchEnvironment: function(envId) {
    const { setDebugEnvironmentId } = require('./config/environment.js');
    if (setDebugEnvironmentId(envId)) {
      // 重新初始化环境
      this.initializeEnvironment();
      return true;
    }
    return false;
  },

  /**
   * 用户登录方法
   * 根据环境配置选择云开发登录或模拟登录
   * 注意：不再自动从缓存恢复登录状态，需要用户主动登录
   * @param {function} callback 登录完成回调函数
   */
  login: function(callback) {
    console.log('开始用户登录流程');

    // 根据环境配置选择登录方式
    const envConfig = this.getEnvironmentConfig();

    if (envConfig.useCloudFunction && this.globalData.cloudInitialized) {
      // 使用云开发登录
      this.performCloudLogin(callback);
    } else {
      // 使用模拟登录
      this.performMockLogin(callback);
    }
  },

  /**
   * 执行云开发登录
   * @param {function} callback 登录完成回调函数
   */
  performCloudLogin: function(callback) {
    console.log('[云开发] 开始微信登录流程');

    // 先检查session状态
    wx.checkSession({
      success: () => {
        console.log('[云开发] session有效，继续登录流程');
        this.executeWxLogin(callback);
      },
      fail: () => {
        console.log('[云开发] session失效，重新获取登录凭证');
        this.executeWxLogin(callback);
      }
    });
  },

  /**
   * 执行微信登录获取code
   * @param {function} callback 登录完成回调函数
   */
  executeWxLogin: function(callback) {
    // 获取微信登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('[云开发] 获取登录凭证成功');
          // 获取用户信息
          this.getUserProfileWithRetry(loginRes.code, callback);
        } else {
          console.error('[云开发] 微信登录失败:', loginRes.errMsg);
          this.handleLoginError('微信登录失败', callback);
        }
      },
      fail: (error) => {
        console.error('[云开发] wx.login调用失败:', error);
        this.handleLoginError(error, callback);
      }
    });
  },

  /**
   * 获取用户信息（带重试机制）
   * @param {string} code 登录凭证
   * @param {function} callback 登录完成回调函数
   * @param {number} retryCount 重试次数
   */
  getUserProfileWithRetry: function(code, callback, retryCount = 0) {
    const maxRetries = 2; // 最大重试次数
    
    wx.getUserProfile({
      desc: '用于完善用户资料，提供更好的服务体验',
      success: (userRes) => {
        console.log('[云开发] 获取用户信息成功');
        // 调用云函数进行登录
        this.callCloudLogin(code, userRes.userInfo, callback);
      },
      fail: (error) => {
        console.error('[云开发] 获取用户信息失败:', error);

        // 检查是否是access_token过期错误
        if (error.errMsg && error.errMsg.includes('access_token expired')) {
          console.log('[云开发] 检测到access_token过期');
          
          if (retryCount < maxRetries) {
            console.log(`[云开发] 尝试重新获取用户信息 (${retryCount + 1}/${maxRetries})`);
            // 延迟重试
            setTimeout(() => {
              this.getUserProfileWithRetry(code, callback, retryCount + 1);
            }, 1000 * (retryCount + 1)); // 递增延迟
          } else {
            console.log('[云开发] 重试次数已达上限，使用匿名登录');
            // 重试次数达到上限，进行匿名登录
            this.callCloudLogin(code, null, callback);
          }
        } else if (error.errMsg && error.errMsg.includes('auth deny')) {
          console.log('[云开发] 用户拒绝授权，使用匿名登录');
          // 用户拒绝授权，进行匿名登录
          this.callCloudLogin(code, null, callback);
        } else {
          console.log('[云开发] 获取用户信息失败:', error.errMsg);
          // 其他错误，提示用户登录失败
          this.handleLoginError(error, callback);
        }
      }
    });
  },

  /**
   * 处理access_token过期错误
   * @param {function} callback 登录完成回调函数
   */
  handleAccessTokenExpired: function(callback) {
    console.log('[云开发] 处理access_token过期错误');

    wx.showModal({
      title: '登录状态过期',
      content: '检测到登录状态已过期，请重新打开小程序或重新登录微信开发者工具',
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试，回退到模拟登录
          console.log('[云开发] 用户选择重试，使用模拟登录');
          this.performMockLogin(callback);
        } else {
          // 用户取消，返回错误
          if (callback) callback(new Error('用户取消登录'));
        }
      }
    });
  },

  /**
   * 处理登录错误
   * @param {string|Error} error 错误信息
   * @param {function} callback 登录完成回调函数
   */
  handleLoginError: function(error, callback) {
    const errorMsg = typeof error === 'string' ? error : error.message || error.errMsg || '登录失败';

    console.error('[云开发] 登录错误:', errorMsg);

    // 检查是否是access_token相关错误
    if (errorMsg.includes('access_token expired') || errorMsg.includes('42001')) {
      this.handleAccessTokenExpired(callback);
    } else {
      // 其他错误，回退到模拟登录
      console.log('[云开发] 登录失败，回退到模拟登录');
      this.performMockLogin(callback);
    }
  },

  /**
   * 调用云函数进行登录
   * @param {string} code 微信登录凭证
   * @param {object} userInfo 用户信息
   * @param {function} callback 登录完成回调函数
   */
  callCloudLogin: function(code, userInfo, callback) {
    // 检查云开发是否已初始化
    if (!this.globalData.cloudInitialized) {
      console.warn('[云开发] 云开发未初始化，使用模拟登录');
      this.performMockLogin(callback);
      return;
    }

    wx.cloud.callFunction({
      name: 'auth',
      data: {
        action: 'login',
        data: {
          code: code,
          userInfo: userInfo
        }
      },
      timeout: 10000, // 设置10秒超时
      success: (res) => {
        console.log('[云开发] 云函数调用成功:', res.result);

        if (res.result && res.result.success) {
          const { token, userInfo: cloudUserInfo, isNewUser } = res.result.data;

          console.log('[云开发] 登录成功:', {
            userId: cloudUserInfo.id,
            nickname: cloudUserInfo.nickname,
            isNewUser: isNewUser,
            cloudEnvId: this.getEnvironmentConfig().cloudEnvId
          });

          // 设置登录状态
          this.setLoginStatus(token, cloudUserInfo);
          this.processLoginCallbacks();

          // 记录云开发登录成功日志
          console.log('[云开发] 用户数据已存储到云数据库:', {
            collection: 'users',
            userId: cloudUserInfo.id,
            openid: cloudUserInfo.openid,
            nickname: cloudUserInfo.nickname,
            timestamp: new Date().toISOString()
          });

          if (callback) callback(null, { token, userInfo: cloudUserInfo, isNewUser });
        } else {
          const errorMsg = res.result ? res.result.error : '未知错误';
          console.error('[云开发] 登录失败:', errorMsg);

          // 显示错误提示
          wx.showToast({
            title: '登录失败',
            icon: 'none',
            duration: 2000
          });

          // 云登录失败时回退到模拟登录
          console.log('[云开发] 回退到模拟登录');
          this.performMockLogin(callback);
        }
      },
      fail: (error) => {
        console.error('[云开发] 云函数调用失败:', error);

        // 显示错误提示
        wx.showToast({
          title: '网络连接失败',
          icon: 'none',
          duration: 2000
        });

        // 检查是否是网络错误
        if (error.errMsg && error.errMsg.includes('Failed to fetch')) {
          console.log('[云开发] 网络连接失败，回退到模拟登录');
        } else if (error.errMsg && error.errMsg.includes('timeout')) {
          console.log('[云开发] 请求超时，回退到模拟登录');
        } else {
          console.log('[云开发] 其他错误，回退到模拟登录');
        }

        // 所有失败情况都回退到模拟登录
        this.performMockLogin(callback);
      }
    });
  },

  /**
   * 执行模拟登录（开发环境使用）
   * @param {function} callback 登录完成回调函数
   */
  performMockLogin: function(callback) {
    console.log('[模拟登录] 执行前端模拟登录');
    
    const mockToken = 'mock_jwt_token_frontend_only_' + Date.now();
    const mockUserInfo = {
      id: 1,
      nickname: '本地测试用户',
      avatar_url: '/images/avatar.png',
      openid: 'mock_openid_' + Date.now()
    };

    this.setLoginStatus(mockToken, mockUserInfo);
    this.processLoginCallbacks();
    if (callback) callback(null, { token: mockToken, userInfo: mockUserInfo });
  },

  // [NEW] Process the callback queue
  processLoginCallbacks: function() {
    if (this.loginReadyCallback.length > 0) {
      this.loginReadyCallback.forEach(callback => callback());
      // Clear the queue after execution
      this.loginReadyCallback = [];
    }
  },

  /**
   * [RECONSTRUCTED] Set login status with token and user info
   * @param {string} token - JWT token from server
   * @param {object} userInfo - User info from server
   */
  setLoginStatus: function(token, userInfo) {
    try {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      this.globalData.isLoggedIn = true;

      wx.setStorageSync('token', token);
      wx.setStorageSync('userInfo', userInfo);
      console.log('Login status set successfully.');
    } catch (error) {
      console.error('Failed to set login status:', error);
    }
  },

  /**
   * 清除登录状态
   * @param {boolean} forceComplete 是否强制完全清除（包括所有相关存储）
   */
  clearLoginStatus: function(forceComplete = false) {
    try {
      // 清除全局数据
      this.globalData.userInfo = null;
      this.globalData.token = null;
      this.globalData.isLoggedIn = false;

      // 清除本地存储
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');

      // 如果是强制完全清除，也清除用户偏好等数据
      if (forceComplete) {
        wx.removeStorageSync('userPreferences');
        // 重置全局偏好数据
        this.globalData.preferences = {
          likes: [],
          dislikes: [],
          canEat: [],
          cannotEat: []
        };
        console.log('已强制清除所有用户相关数据');
      }

      // 如果使用云开发，尝试调用云函数登出（可选）
      if (this.globalData.useCloudFunction && this.globalData.cloudInitialized) {
        this.performCloudLogout();
      }

      console.log('登录状态清除成功');
    } catch (error) {
      console.error('清除登录状态失败:', error);
      // 即使出错也要确保基本的登录状态被清除
      this.globalData.userInfo = null;
      this.globalData.token = null;
      this.globalData.isLoggedIn = false;
    }
  },

  /**
   * 执行云开发登出（可选操作，失败不影响本地登出）
   */
  performCloudLogout: function() {
    try {
      // 异步调用云函数登出，不等待结果
      wx.cloud.callFunction({
        name: 'auth',
        data: {
          action: 'logout'
        },
        success: (res) => {
          console.log('[云开发] 云端登出成功:', res.result);
        },
        fail: (error) => {
          console.warn('[云开发] 云端登出失败（不影响本地登出）:', error);
        }
      });
    } catch (error) {
      console.warn('[云开发] 云端登出调用异常（不影响本地登出）:', error);
    }
  },

  // --- Preference and other functions remain the same for now ---

  loadUserPreferences: function() {
    try {
      const preferences = wx.getStorageSync('userPreferences');
      if (preferences) {
        this.globalData.preferences = preferences;
      }
    } catch (e) {
      console.error('Failed to load user preferences:', e);
    }
  },

  saveUserPreferences: function() {
    try {
      wx.setStorageSync('userPreferences', this.globalData.preferences);
    } catch (e) {
      console.error('Failed to save user preferences:', e);
    }
  },

  // getRandomFood will be replaced by a backend call later
  getRandomFood: function() {
    // This is now a placeholder. The real logic will be a wx.request to the backend.
    console.warn('getRandomFood is using mock data. Should be replaced by API call.');
    const mockFood = { id: 1, name: '麻辣烫 (Mock)', category: '中式', tags: ['辣', '热汤'] };
    return mockFood;
  }
});