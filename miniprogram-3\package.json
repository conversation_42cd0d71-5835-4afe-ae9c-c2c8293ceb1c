{"name": "miniprogram-frontend", "version": "1.0.0", "description": "WeChat miniprogram frontend with multi-environment support", "main": "app.js", "scripts": {"dev": "echo \"请使用微信开发者工具打开项目目录\"", "build": "echo \"小程序无需构建，直接使用源码\"", "deploy": "node scripts/deploy-frontend.js", "deploy:dev": "node scripts/deploy-frontend.js --env=dev", "deploy:test": "node scripts/deploy-frontend.js --env=test", "deploy:staging": "node scripts/deploy-frontend.js --env=staging", "deploy:prod": "node scripts/deploy-frontend.js --env=prod --force", "deploy:dry-run": "node scripts/deploy-frontend.js --env=dev --dry-run", "upload:dev": "node scripts/deploy-frontend.js --env=dev --upload", "upload:test": "node scripts/deploy-frontend.js --env=test --upload", "upload:staging": "node scripts/deploy-frontend.js --env=staging --upload", "upload:prod": "node scripts/deploy-frontend.js --env=prod --upload --force", "preview:dev": "node scripts/deploy-frontend.js --env=dev --preview", "preview:test": "node scripts/deploy-frontend.js --env=test --preview", "preview:staging": "node scripts/deploy-frontend.js --env=staging --preview", "env:list": "node -e \"console.log(require('./config/environment').getAvailableEnvironments())\"", "env:current": "node -e \"console.log(require('./config/environment').getCurrentEnvironmentId())\"", "env:config": "node -e \"const env = process.argv[2] || 'dev'; console.log(JSON.stringify(require('./config/environment').getEnvironmentConfig(env), null, 2))\" --", "env:switch": "node scripts/env-switch.js", "env:clear": "node -e \"const { clearDebugEnvironmentId } = require('./config/environment'); clearDebugEnvironmentId(); console.log('已清除调试环境ID');\"", "cloud:deploy": "node scripts/deploy-cloud.js deploy", "cloud:deploy:dev": "node scripts/deploy-cloud.js deploy dev", "cloud:deploy:prod": "node scripts/deploy-cloud.js deploy prod", "cloud:init-db": "node scripts/deploy-cloud.js init-db", "cloud:init-db:dev": "node scripts/deploy-cloud.js init-db dev", "cloud:init-db:prod": "node scripts/deploy-cloud.js init-db prod", "cloud:full": "node scripts/deploy-cloud.js full", "cloud:full:dev": "node scripts/deploy-cloud.js full dev", "cloud:full:prod": "node scripts/deploy-cloud.js full prod", "cloud:status": "node scripts/deploy-cloud.js status", "cloud:status:dev": "node scripts/deploy-cloud.js status dev", "cloud:status:prod": "node scripts/deploy-cloud.js status prod", "cloud:logs": "node scripts/deploy-cloud.js logs"}, "keywords": ["wechat", "miniprogram", "frontend", "multi-environment"], "author": "Your Name", "license": "MIT", "devDependencies": {"miniprogram-cli": "^1.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "your-repository-url"}, "bugs": {"url": "your-repository-url/issues"}, "homepage": "your-repository-url#readme"}