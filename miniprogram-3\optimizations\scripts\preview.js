/**
 * 小程序预览脚本
 * 自动化小程序预览流程
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const QRCode = require('qrcode');

class MiniProgramPreviewer {
  constructor() {
    this.projectPath = path.resolve(__dirname, '..');
    this.configPath = path.join(this.projectPath, 'project.config.json');
  }

  /**
   * 生成预览
   * @param {Object} options 预览选项
   */
  async preview(options = {}) {
    const {
      desc = '预览版本',
      qrFormat = 'terminal',
      qrOutput = './preview-qr.png',
      pagePath = '',
      searchQuery = ''
    } = options;

    console.log('👀 开始生成预览...');
    console.log('描述: ' + desc);

    try {
      // 检查项目配置
      this.checkProjectConfig();

      // 构建命令
      const command = [
        'wx',
        'preview',
        '--project "' + this.projectPath + '"',
        '--desc "' + desc + '"',
        '--qr-format ' + qrFormat,
        '--qr-output "' + qrOutput + '"'
      ];

      // 添加页面路径参数
      if (pagePath) {
        command.push('--page-path "' + pagePath + '"');
      }

      // 添加搜索参数
      if (searchQuery) {
        command.push('--search-query "' + searchQuery + '"');
      }

      const fullCommand = command.join(' ');
      console.log('执行命令:', fullCommand);

      // 执行预览
      const result = execSync(fullCommand, {
        encoding: 'utf8',
        cwd: this.projectPath,
        stdio: 'inherit'
      });

      console.log('✅ 预览生成成功!');
      
      // 如果生成了二维码文件，显示路径
      if (qrFormat === 'image' && fs.existsSync(qrOutput)) {
        console.log('📱 二维码已保存到: ' + qrOutput);
      }

      return result;

    } catch (error) {
      console.error('❌ 预览生成失败:', error.message);
      throw error;
    }
  }

  /**
   * 检查项目配置
   */
  checkProjectConfig() {
    if (!fs.existsSync(this.configPath)) {
      throw new Error('project.config.json 文件不存在');
    }

    const config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
    
    if (!config.appid) {
      throw new Error('project.config.json 中缺少 appid 配置');
    }

    console.log('项目 ID: ' + config.appid);
  }

  /**
   * 生成自定义二维码
   * @param {string} url 二维码内容
   * @param {string} outputPath 输出路径
   */
  async generateCustomQR(url, outputPath) {
    try {
      await QRCode.toFile(outputPath, url, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      console.log('✅ 自定义二维码已生成: ' + outputPath);
    } catch (error) {
      console.error('❌ 二维码生成失败:', error.message);
    }
  }
}

// 命令行调用
if (require.main === module) {
  const previewer = new MiniProgramPreviewer();
  
  // 解析命令行参数
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    options[key] = value;
  }
  
  previewer.preview(options)
    .then(() => {
      console.log('🎉 预览完成!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 预览失败:', error.message);
      process.exit(1);
    });
}

module.exports = MiniProgramPreviewer;
