/* pages/publish/publish.wxss */
/* 发布页面样式 - Publish Page Styles */
/* 极简黑白风格设计 - Minimalist Black & White Style */

/* 精细化设计规范 - Refined Design Standards */
/* 字体大小：18%缩小后的精确数值 */
/* 统一圆角尺寸：8rpx, 12rpx, 16rpx */
/* 统一间距尺寸：8rpx, 12rpx, 16rpx, 20rpx, 24rpx */
/* 响应式适配：确保在不同屏幕尺寸下的一致性 */

/* 整体容器 - Main Container */
.publish-container {
  background-color: #ffffff;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  display: flex;
  flex-direction: column; /* 优化：使用flex布局确保内容区域正确填充 */
}

/* 顶部导航栏 - Top Navigation */
.nav-bar {
  display: flex;
  justify-content: center; /* 修改：标题居中显示 */
  align-items: center;
  padding: 12rpx 20rpx; /* 精细化调整：增加内边距确保触摸体验，上下12rpx，左右20rpx */
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10000; /* 修复：提高导航栏层级，确保不被加载遮罩遮挡 */
  min-height: 88rpx; /* 精细化调整：恢复合适的导航栏高度，确保触摸友好 */
  flex-shrink: 0; /* 优化：防止导航栏被压缩 */
}

.nav-title {
  font-size: 39rpx; /* 统一字体：与首页标题层次保持一致 */
  font-weight: 600; /* 半粗体字重，保持标题重要性 */
  color: #000000;
  text-align: center;
}

/* 内容滚动区域 - Content Scroll Area */
.content-scroll {
  flex: 1; /* 优化：使用flex布局自动填充剩余空间 */
  padding: 20rpx; /* 统一间距：与其他页面保持一致 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* 为底部操作栏留出空间 */
  overflow-y: auto; /* 优化：确保内容可滚动 */
}

/* 文本输入区域 - Text Input Area */
.text-section {
  margin-bottom: 24rpx; /* 精细化调整：恢复合适的区域间距 */
}

.text-input {
  width: 100%;
  min-height: 160rpx; /* 精细化调整：恢复合适的输入框高度，提升用户体验 */
  padding: 16rpx; /* 精细化调整：恢复合适的内边距，确保文字不贴边 */
  background-color: #f8f8f8;
  border-radius: 12rpx; /* 精细化调整：使用统一的圆角设计 */
  font-size: 30rpx; /* 统一字体：调整为标准正文大小 */
  line-height: 1.5; /* 精细化调整：恢复舒适的行高，提升可读性 */
  color: #000000;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-sizing: border-box; /* 优化：确保尺寸计算正确 */
}

.text-input:focus {
  background-color: #ffffff;
  border-color: #000000;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1); /* 优化：增强聚焦状态的视觉反馈 */
  transform: translateY(-1rpx); /* 优化：添加微妙的上浮效果 */
}

.placeholder-style {
  color: #999999;
  font-size: 30rpx; /* 统一字体：调整为标准正文大小 */
}

.char-count {
  text-align: right;
  margin-top: 8rpx; /* 精细化调整：恢复合适的上边距 */
  font-size: 18rpx; /* 统一字体：辅助文字大小 */
  color: #999999;
}

/* 区域标题 - Section Title */
.section-title {
  display: block;
  font-size: 32rpx; /* 统一字体：调整为H3标准 */
  font-weight: 600; /* 半粗体字重，保持标题重要性 */
  color: #000000;
  margin-bottom: 16rpx; /* 精细化调整：恢复合适的底部间距 */
}

/* 图片上传区域 - Image Upload Area */
.image-section {
  margin-bottom: 24rpx; /* 精细化调整：恢复合适的区域间距 */
}

.image-grid {
  display: grid; /* 优化：使用grid布局确保图片排列整齐 */
  grid-template-columns: repeat(3, 1fr); /* 优化：每行显示3张图片 */
  gap: 12rpx; /* 精细化调整：恢复合适的网格间距 */
  justify-items: center; /* 优化：图片居中对齐 */
}

.image-item {
  position: relative;
  width: 200rpx; /* 精细化调整：增大图片尺寸，提升视觉效果 */
  height: 200rpx; /* 精细化调整：增大图片尺寸，提升视觉效果 */
  border-radius: 12rpx; /* 精细化调整：使用统一的圆角设计 */
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1); /* 优化：添加阴影增强层次感 */
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease; /* 优化：添加图片交互动画 */
}

.image-item:active .preview-image {
  transform: scale(0.95); /* 优化：添加按压缩放效果 */
}

.delete-btn {
  position: absolute;
  top: 4rpx; /* 精细化调整：增加顶部距离，避免误触 */
  right: 4rpx; /* 精细化调整：增加右侧距离，避免误触 */
  width: 48rpx; /* 精细化调整：增大触摸目标，提升用户体验 */
  height: 48rpx; /* 精细化调整：增大触摸目标，提升用户体验 */
  background-color: rgba(0, 0, 0, 0.8); /* 精细化调整：增加透明度，提升视觉效果 */
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease; /* 优化：添加过渡动画 */
}

.delete-btn::after {
  border: none;
}

.delete-btn:active {
  background-color: rgba(255, 0, 0, 0.8); /* 优化：按压时显示红色，增强删除操作的视觉反馈 */
  transform: scale(1.1); /* 优化：按压时放大，增强交互反馈 */
}

.delete-icon {
  width: 20rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  height: 20rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  filter: brightness(0) invert(1); /* 优化：确保图标为白色 */
}

.add-image-btn {
  width: 200rpx; /* 精细化调整：与图片项保持一致的尺寸 */
  height: 200rpx; /* 精细化调整：与图片项保持一致的尺寸 */
  background-color: #f8f8f8;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx; /* 精细化调整：使用统一的圆角设计 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-image-btn::after {
  border: none;
}

.add-image-btn:active {
  background-color: #f0f0f0;
  border-color: #999999;
  transform: scale(0.95); /* 优化：添加按压缩放效果 */
}

.add-icon {
  width: 40rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  height: 40rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  margin-bottom: 8rpx; /* 精细化调整：恢复合适的底部间距 */
  opacity: 0.6;
}

.add-text {
  font-size: 18rpx; /* 统一字体：辅助文字大小 */
  color: #666666;
}

.image-tip {
  margin-top: 12rpx; /* 精细化调整：恢复合适的顶部间距 */
  font-size: 18rpx; /* 统一字体：提示文字大小 */
  color: #999999;
  text-align: center; /* 优化：提示文字居中显示 */
}

/* 位置选择区域 - Location Selection Area */
.location-section {
  margin-bottom: 24rpx; /* 精细化调整：恢复合适的区域间距 */
}

.location-btn {
  width: 100%;
  background-color: #f8f8f8;
  border: none;
  border-radius: 12rpx; /* 精细化调整：使用统一的圆角设计 */
  padding: 16rpx; /* 精细化调整：恢复合适的内边距，确保触摸体验 */
  display: flex;
  align-items: center;
  text-align: left;
  transition: all 0.3s ease;
  min-height: 80rpx; /* 精细化调整：恢复合适的最小高度，确保触摸友好 */
  box-sizing: border-box; /* 优化：确保尺寸计算正确 */
}

.location-btn::after {
  border: none;
}

.location-btn:active {
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1); /* 优化：增强按压阴影效果 */
  transform: translateY(1rpx); /* 优化：添加微妙的下沉效果 */
}

.location-icon {
  width: 32rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  height: 32rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  margin-right: 12rpx; /* 精细化调整：恢复合适的右边距 */
  opacity: 0.6;
}

.location-content {
  flex: 1;
  min-width: 0; /* 优化：防止内容溢出 */
}

.location-text {
  font-size: 29rpx; /* 统一字体：与输入框文字保持一致 */
  color: #999999;
}

.location-name {
  display: block;
  font-size: 29rpx; /* 统一字体：位置名称 */
  color: #000000;
  font-weight: 500; /* 中等字重，精细化设计 */
  margin-bottom: 2rpx; /* 精细化调整：恢复合适的底部间距 */
  overflow: hidden; /* 优化：防止文字溢出 */
  text-overflow: ellipsis; /* 优化：文字溢出时显示省略号 */
  white-space: nowrap; /* 优化：防止文字换行 */
}

.location-address {
  display: block;
  font-size: 22rpx; /* 统一字体：位置地址辅助文字 */
  color: #666666;
  overflow: hidden; /* 优化：防止文字溢出 */
  text-overflow: ellipsis; /* 优化：文字溢出时显示省略号 */
  white-space: nowrap; /* 优化：防止文字换行 */
}

.arrow-icon {
  width: 20rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  height: 20rpx; /* 精细化调整：恢复合适的图标尺寸，确保清晰可见 */
  opacity: 0.4;
  flex-shrink: 0; /* 优化：防止图标被压缩 */
}

/* 分类标签选择区域 - Category Tags Selection Area */
.category-section {
  margin-bottom: 24rpx; /* 精细化调整：恢复合适的区域间距 */
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx; /* 精细化调整：恢复合适的标签间距，确保视觉清晰 */
  justify-content: flex-start; /* 优化：标签左对齐 */
}

.category-tag {
  padding: 12rpx 20rpx; /* 精细化调整：恢复合适的内边距，确保触摸体验 */
  background-color: #f8f8f8;
  border: 2rpx solid transparent;
  border-radius: 24rpx; /* 精细化调整：恢复圆润的圆角设计 */
  font-size: 22rpx; /* 统一字体：分类标签文字 */
  transition: all 0.3s ease;
  min-height: 60rpx; /* 精细化调整：恢复合适的最小高度，确保触摸友好 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box; /* 优化：确保尺寸计算正确 */
}

.category-tag::after {
  border: none;
}

.category-tag:active {
  transform: scale(0.95); /* 优化：添加按压缩放效果 */
}

.category-tag.active {
  background-color: #000000;
  border-color: #000000;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2); /* 优化：增强激活状态的阴影效果 */
  transform: translateY(-1rpx); /* 优化：激活状态微妙上浮 */
}

.category-text {
  color: #666666;
  font-weight: 500;
  white-space: nowrap; /* 优化：防止文字换行 */
}

.category-tag.active .category-text {
  color: #ffffff;
  font-weight: 600;
}

/* 其他设置区域 - Other Settings Area */
.settings-section {
  margin-bottom: 24rpx; /* 精细化调整：恢复合适的区域间距 */
  background-color: #f8f8f8; /* 优化：添加背景色区分设置区域 */
  border-radius: 12rpx; /* 优化：添加圆角设计 */
  padding: 16rpx; /* 优化：添加内边距 */
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0; /* 精细化调整：恢复合适的内边距，确保触摸体验 */
  border-bottom: 1rpx solid #e8e8e8; /* 优化：调整分割线颜色 */
  min-height: 80rpx; /* 精细化调整：恢复合适的最小高度，确保触摸友好 */
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 29rpx; /* 统一字体：设置标签文字 */
  color: #000000;
  font-weight: 500; /* 中等字重，精细化设计 */
}

.setting-switch {
  transform: scale(0.9); /* 精细化调整：稍微增大开关尺寸，提升操作体验 */
}

/* 底部安全区域 - Bottom Safe Area */
.safe-area {
  height: calc(env(safe-area-inset-bottom) + 40rpx); /* 优化：动态计算安全区域高度，适配不同设备 */
}

/* 加载遮罩 - Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 88rpx; /* 修复：从导航栏下方开始，避免遮挡导航栏 */
  left: 0;
  width: 100%;
  height: calc(100% - 88rpx); /* 修复：调整高度，避免覆盖导航栏 */
  background-color: rgba(0, 0, 0, 0.7); /* 精细化调整：增加遮罩透明度，提升视觉效果 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4rpx); /* 优化：添加背景模糊效果 */
}

.loading-content {
  background-color: #ffffff;
  border-radius: 16rpx; /* 精细化调整：增大圆角，提升现代感 */
  padding: 32rpx; /* 精细化调整：恢复合适的内边距 */
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15); /* 精细化调整：增强阴影效果 */
  min-width: 200rpx; /* 优化：设置最小宽度，确保视觉平衡 */
}

.loading-icon {
  width: 56rpx; /* 精细化调整：恢复合适的加载图标尺寸 */
  height: 56rpx; /* 精细化调整：恢复合适的加载图标尺寸 */
  margin-bottom: 16rpx; /* 精细化调整：恢复合适的底部间距 */
  animation: loading-spin 1s linear infinite; /* 优化：添加旋转动画 */
}

.loading-text {
  font-size: 29rpx; /* 统一字体：加载文字 */
  color: #000000;
  font-weight: 500; /* 中等字重，精细化设计 */
}

/* 加载动画 - Loading Animation */
@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式适配 - Responsive Design */
@media (max-width: 400px) {
  .image-grid {
    grid-template-columns: repeat(2, 1fr); /* 小屏幕每行显示2张图片 */
  }

  .image-item,
  .add-image-btn {
    width: 180rpx; /* 小屏幕减小图片尺寸 */
    height: 180rpx;
  }

  .category-grid {
    gap: 8rpx; /* 小屏幕减小标签间距 */
  }

  .category-tag {
    padding: 10rpx 16rpx; /* 小屏幕减小标签内边距 */
    font-size: 20rpx; /* 小屏幕减小标签文字 */
  }

  .nav-title {
    font-size: 32rpx; /* 小屏幕减小导航标题 */
  }

  .section-title {
    font-size: 26rpx; /* 小屏幕减小章节标题 */
  }

  .text-input,
  .placeholder-style {
    font-size: 26rpx; /* 小屏幕减小输入框文字 */
  }
}

/* 错误提示样式 - Error Message Styles */
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  background: white;
  margin: 40rpx;
  padding: 60rpx 40rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 40rpx;
  line-height: 1.5;
  display: block;
}

.retry-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.retry-btn:active {
  background: #ff5252;
  transform: translateY(2rpx);
}

/* 底部操作栏 - Bottom Action Bar */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  padding: 16rpx 20rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
  display: flex;
  gap: 16rpx;
  z-index: 1000;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1); /* 添加顶部阴影 */
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn::after {
  border: none;
}

.action-btn:active {
  transform: scale(0.98);
}

.cancel-btn {
  background-color: #f8f8f8;
  border: 2rpx solid #e0e0e0;
}

.cancel-btn .btn-text {
  color: #666666;
}

.cancel-btn:active {
  background-color: #f0f0f0;
}

.publish-btn.active {
  background-color: #000000;
  border: 2rpx solid #000000;
}

.publish-btn.active .btn-text {
  color: #ffffff;
}

.publish-btn.active:active {
  background-color: #333333;
}

.publish-btn.disabled {
  background-color: #f8f8f8;
  border: 2rpx solid #e0e0e0;
}

.publish-btn.disabled .btn-text {
  color: #cccccc;
}

.btn-text {
  font-size: 32rpx; /* 统一字体：调整为重要正文大小 */
  font-weight: 600;
}

/* 重复样式已移除，保持代码简洁性 */
