/* pages/browse-history/browse-history.wxss */
/* 浏览历史页面样式 */

/* 全局样式 */
.history-container {
  min-height: 100vh;
  background-color: #ffffff;
  color: #000000;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-action {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.nav-action:active {
  background-color: #f5f5f5;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.8;
}

/* 搜索栏 */
.search-bar {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
  background: transparent;
}

.search-clear {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 统计信息 */
.stats-section {
  padding: 32rpx;
  background-color: #fafafa;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  font-size: 40rpx;
  font-weight: 600;
  color: #000000;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 操作栏 */
.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-info {
  padding: 12rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.filter-text {
  font-size: 26rpx;
  color: #666666;
}

.action-right {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  background-color: #000000;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.action-btn.danger {
  background-color: #ff4d4f;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-text {
  font-size: 26rpx;
  color: #ffffff;
}

/* 编辑工具栏 */
.edit-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #e8e8e8;
}

.select-all {
  padding: 12rpx 20rpx;
  background-color: #ffffff;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
}

.select-text {
  font-size: 26rpx;
  color: #000000;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}

.toolbar-btn.delete {
  background-color: #ff4d4f;
}

.btn-icon {
  width: 28rpx;
  height: 28rpx;
}

.btn-text {
  font-size: 26rpx;
  color: #ffffff;
}

/* 历史记录列表 */
.history-list {
  flex: 1;
  background-color: #ffffff;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #000000;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 历史记录分组 */
.history-groups {
  padding: 0 32rpx;
}

.history-group {
  margin-bottom: 40rpx;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.group-date {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.group-count {
  font-size: 24rpx;
  color: #999999;
}

/* 历史记录项 */
.history-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.history-item.edit-mode {
  padding-left: 80rpx;
}

.history-item.selected {
  border-color: #000000;
  background-color: #fafafa;
}

.history-item:active {
  transform: scale(0.98);
}

/* 选择框 */
.select-checkbox {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background-color: #000000;
  border-color: #000000;
}

.check-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox.checked .check-icon {
  opacity: 1;
}

/* 类型图标 */
.type-icon {
  position: relative;
  flex-shrink: 0;
}

.icon-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
}

.type-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.type-badge.food {
  background-color: #ff9500;
}

.type-badge.post {
  background-color: #1890ff;
}

.type-badge.user {
  background-color: #52c41a;
}

.badge-text {
  color: #ffffff;
  font-weight: 500;
}

/* 内容信息 */
.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 0;
}

.content-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 24rpx;
  color: #999999;
}

/* 作者信息 */
.author-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.author-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
}

.author-name {
  font-size: 24rpx;
  color: #666666;
}

/* 用户内容 */
.user-name {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.verified-icon {
  width: 24rpx;
  height: 24rpx;
  background-color: #1890ff;
  border-radius: 50%;
  padding: 2rpx;
}

.user-stats {
  display: flex;
  gap: 20rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #999999;
}

/* 浏览信息 */
.view-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
  flex-shrink: 0;
}

.view-time {
  font-size: 24rpx;
  color: #999999;
}

.view-stats {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.view-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #cccccc;
}

.stat-value {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

/* 删除按钮 */
.delete-action {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(255, 77, 79, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.delete-action:active {
  background-color: rgba(255, 77, 79, 0.2);
  transform: scale(0.9);
}

.delete-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.8;
}

/* 加载状态 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666666;
}

/* 底部安全区域 */
.safe-area {
  height: 60rpx;
  background-color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .nav-header {
    padding: 16rpx 24rpx;
  }
  
  .search-bar {
    padding: 20rpx 24rpx;
  }
  
  .stats-section {
    padding: 28rpx 24rpx;
  }
  
  .action-bar {
    padding: 16rpx 24rpx;
  }
  
  .edit-toolbar {
    padding: 16rpx 24rpx;
  }
  
  .history-groups {
    padding: 0 24rpx;
  }
  
  .history-item {
    padding: 20rpx;
  }
  
  .history-item.edit-mode {
    padding-left: 76rpx;
  }
  
  .icon-image {
    width: 72rpx;
    height: 72rpx;
  }
}

/* 动画效果 */
.history-item {
  transition: all 0.2s ease;
}

.nav-action:active,
.action-btn:active,
.select-all:active {
  transform: scale(0.95);
}

/* 深色模式适配（预留） */
@media (prefers-color-scheme: dark) {
  .history-container {
    background-color: #000000;
    color: #ffffff;
  }
  
  .nav-header,
  .search-bar,
  .action-bar,
  .history-list {
    background-color: #000000;
    border-color: #333333;
  }
  
  .nav-title {
    color: #ffffff;
  }
  
  .stats-section {
    background-color: #1a1a1a;
  }
  
  .stat-number {
    color: #ffffff;
  }
  
  .action-btn {
    background-color: #ffffff;
  }
  
  .action-text {
    color: #000000;
  }
  
  .action-btn.danger {
    background-color: #ff4d4f;
  }
  
  .action-btn.danger .action-text {
    color: #ffffff;
  }
  
  .group-date {
    color: #ffffff;
  }
  
  .history-item {
    background-color: #000000;
    border-color: #333333;
  }
  
  .history-item.selected {
    background-color: #1a1a1a;
    border-color: #ffffff;
  }
  
  .content-title {
    color: #ffffff;
  }
  
  .checkbox {
    background-color: #000000;
    border-color: #666666;
  }
  
  .checkbox.checked {
    background-color: #ffffff;
    border-color: #ffffff;
  }
  
  .safe-area {
    background-color: #000000;
  }
}