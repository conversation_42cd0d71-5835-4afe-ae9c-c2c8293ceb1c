// 创建简单的PNG图标工具
// 使用Canvas API生成基础的tabBar图标

const fs = require('fs');
const path = require('path');

// 创建Canvas（需要在Node.js环境中使用canvas库）
function createIcon(iconType, isActive = false, size = 81) {
  // 这里提供图标的SVG路径数据
  const iconPaths = {
    home: {
      // 房子图标路径
      path: 'M40.5 15L20 30V60H35V45H46V60H61V30L40.5 15Z',
      viewBox: '0 0 81 81'
    },
    result: {
      // 搜索图标路径  
      path: 'M35 35m-15 0a15 15 0 1 0 30 0a15 15 0 1 0 -30 0M45 45L60 60',
      viewBox: '0 0 81 81'
    },
    community: {
      // 群组图标路径
      path: 'M25 35C25 30 29 26 34 26C39 26 43 30 43 35C43 40 39 44 34 44C29 44 25 40 25 35ZM47 35C47 30 51 26 56 26C61 26 65 30 65 35C65 40 61 44 56 44C51 44 47 40 47 35ZM20 55C20 50 24 46 29 46H39C44 46 48 50 48 55V60H20V55ZM42 55C42 50 46 46 51 46H61C66 46 70 50 70 55V60H42V55Z',
      viewBox: '0 0 81 81'
    },
    profile: {
      // 用户图标路径
      path: 'M40.5 35C40.5 30 44.5 26 49.5 26C54.5 26 58.5 30 58.5 35C58.5 40 54.5 44 49.5 44C44.5 44 40.5 40 40.5 35ZM25 60C25 50 33 42 43 42H56C66 42 74 50 74 60V65H25V60Z',
      viewBox: '0 0 81 81'
    }
  };

  const color = isActive ? '#000000' : '#666666';
  const iconData = iconPaths[iconType];
  
  if (!iconData) {
    throw new Error(`Unknown icon type: ${iconType}`);
  }

  // 生成SVG字符串
  const svg = `
    <svg width="${size}" height="${size}" viewBox="${iconData.viewBox}" xmlns="http://www.w3.org/2000/svg">
      <path d="${iconData.path}" stroke="${color}" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  `;

  return svg.trim();
}

// 生成所有图标
function generateAllIcons() {
  const icons = ['home', 'result', 'community', 'profile'];
  const imagesDir = path.join(__dirname, '../images');
  
  icons.forEach(iconType => {
    // 生成未选中状态图标
    const normalSvg = createIcon(iconType, false);
    fs.writeFileSync(path.join(imagesDir, `tab-${iconType}.svg`), normalSvg);
    
    // 生成选中状态图标
    const activeSvg = createIcon(iconType, true);
    fs.writeFileSync(path.join(imagesDir, `tab-${iconType}-active.svg`), activeSvg);
    
    console.log(`Generated ${iconType} icons`);
  });
  
  console.log('All icons generated successfully!');
}

// 如果直接运行此脚本
if (require.main === module) {
  generateAllIcons();
}

module.exports = { createIcon, generateAllIcons };
