// 重构后的 app.js
// 导入管理器模块
const LifecycleManager = require('./utils/lifecycle-manager');
const GlobalDataManager = require('./utils/global-data-manager');
const ErrorHandler = require('./utils/error-handler');

// 创建管理器实例
const lifecycleManager = new LifecycleManager();
const globalDataManager = new GlobalDataManager();

App({
  // 全局数据管理器
  globalDataManager,
  
  // 生命周期管理器
  lifecycleManager,
  
  // 应用启动
  onLaunch(options) {
    try {
      lifecycleManager.handleLaunch(options);
    } catch (error) {
      ErrorHandler.handleError('应用启动失败', error);
    }
  },

  // 应用显示
  onShow(options) {
    try {
      lifecycleManager.handleShow(options);
    } catch (error) {
      ErrorHandler.handleError('应用显示失败', error);
    }
  },

  // 应用隐藏
  onHide() {
    try {
      lifecycleManager.handleHide();
    } catch (error) {
      ErrorHandler.handleError('应用隐藏失败', error);
    }
  },

  // 应用错误
  onError(error) {
    lifecycleManager.handleError(error);
  },

  // 页面不存在
  onPageNotFound(res) {
    console.warn('页面不存在:', res);
    // 重定向到首页或错误页面
    wx.redirectTo({
      url: '/pages/index/index'
    });
  },

  // 获取全局数据
  getGlobalData(key) {
    return globalDataManager.get(key);
  },

  // 设置全局数据
  setGlobalData(key, value) {
    globalDataManager.set(key, value);
  },

  // 批量设置全局数据
  setMultipleGlobalData(dataObj) {
    globalDataManager.setMultiple(dataObj);
  },

  // 观察全局数据变化
  observeGlobalData(key, callback) {
    globalDataManager.observe(key, callback);
  },

  // 缓存数据
  cacheData(key, value, ttl) {
    globalDataManager.cache(key, value, ttl);
  },

  // 获取缓存数据
  getCacheData(key) {
    return globalDataManager.getCache(key);
  },

  // 全局数据（保持向后兼容）
  globalData: {
    userInfo: null,
    isLoggedIn: false
  }
});