<!--pages/result/result.wxml-->
<view class="phone-container" wx:if="{{dish.name}}">
    <view class="content-wrapper">
        <!-- 1. 顶部标题栏 -->
        <view class="title-bar">
            <text class="title">今天吃什么</text>
        </view>

        <!-- 2. 主推菜品区 -->
        <view class="main-dish-area">
            <view class="dish-card-simple">{{dish.name}}</view>
            <view class="dish-card-core">
                <text class="dish-name">{{dish.subName}}</text>
                <view class="dish-extra-info">
                    <text class="rating">★ {{dish.rating}}分</text>
                    <text class="sales">月售{{dish.sales}}单</text>
                </view>
            </view>
        </view>

        <!-- 3. 菜品详情信息 -->
        <view class="dish-details-tags">
            <text class="tag-item price">¥{{dish.price}}起</text>
            <view class="separator"></view>
            <text class="tag-item">{{dish.deliveryTime}}分钟</text>
            <view class="separator"></view>
            <text class="tag-item promo">{{dish.promo}}</text>
            <view class="separator"></view>
            <text class="tag-item">{{dish.rating}}分</text>
        </view>

        <!-- 4. 平台比价模块 -->
        <view class="price-comparison-module">
            <view class="module-title">平台比价</view>
            <view class="comparison-item" wx:for="{{dish.platforms}}" wx:key="index">
                <view class="platform-logo">{{item.logo}}</view>
                <view class="platform-info">
                    <view class="platform-name">{{item.name}}</view>
                    <view class="platform-price">¥{{item.price}}起</view>
                </view>
                <view class="order-button" bindtap="onOrderTap" data-platform="{{item.name}}">去下单</view>
            </view>
        </view>

        <!-- 5. 社交推荐区 -->
        <view class="social-proof-section">
            <view class="module-title">大家都在吃</view>
            <view class="recommendation-cards">
                <view class="reco-card" wx:for="{{dish.recommendations}}" wx:key="index" bindtap="onRecommendationTap" data-dishname="{{item.dishName}}">
                    <image class="reco-card-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
                    <view class="dish-name">{{item.dishName}}</view>
                    <view class="user-name">{{item.userName}}</view>
                </view>
            </view>
        </view>
    </view>

    <!-- 6. 底部操作栏 -->
    <view class="footer-actions">
        <view class="action-button reroll-button" bindtap="onReroll">重新抽选</view>
        <button class="action-button share-button" open-type="share">分享结果</button>
    </view>
</view>

<view class="container empty-state" wx:else>
    <text>还没有选择哦，快去首页抽一个吧！</text>
</view>
