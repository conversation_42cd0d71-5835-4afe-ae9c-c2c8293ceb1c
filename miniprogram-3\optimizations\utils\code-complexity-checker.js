/**
 * 代码复杂度检查器
 * 自动检查代码复杂度并提供优化建议
 */

const fs = require('fs');
const path = require('path');

class CodeComplexityChecker {
  constructor() {
    this.rules = {
      maxLines: 300,
      maxFunctions: 20,
      maxNesting: 4,
      minCommentRatio: 0.1
    };
  }

  /**
   * 检查文件复杂度
   * @param {string} filePath 文件路径
   * @returns {Object} 检查结果
   */
  checkFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    const result = {
      filePath,
      metrics: this.calculateMetrics(content, lines),
      issues: [],
      suggestions: []
    };
    
    this.analyzeComplexity(result);
    return result;
  }

  /**
   * 计算代码指标
   * @param {string} content 文件内容
   * @param {Array} lines 代码行数组
   * @returns {Object} 代码指标
   */
  calculateMetrics(content, lines) {
    // 计算有效代码行数
    const codeLines = lines.filter(line => {
      const trimmed = line.trim();
      return trimmed.length > 0 && !trimmed.startsWith('//');
    });
    
    // 计算函数数量
    const functionMatches = content.match(/function\s+\w+|\w+\s*[:=]\s*function|\w+\s*[:=]\s*\([^)]*\)\s*=>/g) || [];
    
    // 计算最大嵌套深度
    let maxNesting = 0;
    let currentNesting = 0;
    
    for (const line of lines) {
      const openBraces = (line.match(/{/g) || []).length;
      const closeBraces = (line.match(/}/g) || []).length;
      currentNesting += openBraces - closeBraces;
      maxNesting = Math.max(maxNesting, currentNesting);
    }
    
    // 计算注释行数
    const commentLines = lines.filter(line => {
      const trimmed = line.trim();
      return trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.includes('*/');
    });
    
    const commentRatio = codeLines.length > 0 ? commentLines.length / codeLines.length : 0;
    
    return {
      totalLines: lines.length,
      codeLines: codeLines.length,
      commentLines: commentLines.length,
      functionCount: functionMatches.length,
      maxNesting,
      commentRatio
    };
  }

  /**
   * 分析复杂度并生成建议
   * @param {Object} result 检查结果
   */
  analyzeComplexity(result) {
    const { metrics } = result;
    
    // 检查代码行数
    if (metrics.codeLines > this.rules.maxLines) {
      result.issues.push({
        type: 'lines',
        severity: 'warning',
        message: '代码行数过多 (' + metrics.codeLines + '行)',
        suggestion: '考虑将文件拆分为多个模块'
      });
      
      result.suggestions.push({
        type: 'refactor',
        title: '文件拆分建议',
        description: '将大文件拆分为多个功能模块',
        example: this.generateFileSplitExample()
      });
    }
    
    // 检查函数数量
    if (metrics.functionCount > this.rules.maxFunctions) {
      result.issues.push({
        type: 'functions',
        severity: 'warning',
        message: '函数数量过多 (' + metrics.functionCount + '个)',
        suggestion: '考虑使用模块化设计模式'
      });
      
      result.suggestions.push({
        type: 'modularize',
        title: '模块化建议',
        description: '使用类或模块来组织相关函数',
        example: this.generateModularizationExample()
      });
    }
    
    // 检查嵌套深度
    if (metrics.maxNesting > this.rules.maxNesting) {
      result.issues.push({
        type: 'nesting',
        severity: 'error',
        message: '嵌套深度过深 (' + metrics.maxNesting + '层)',
        suggestion: '使用早期返回模式或提取函数来减少嵌套'
      });
      
      result.suggestions.push({
        type: 'reduce-nesting',
        title: '减少嵌套建议',
        description: '使用早期返回和函数提取来降低复杂度',
        example: this.generateNestingReductionExample()
      });
    }
    
    // 检查注释覆盖率
    if (metrics.commentRatio < this.rules.minCommentRatio) {
      result.issues.push({
        type: 'comments',
        severity: 'info',
        message: '注释覆盖率不足 (' + (metrics.commentRatio * 100).toFixed(1) + '%)',
        suggestion: '添加更多注释来提高代码可读性'
      });
      
      result.suggestions.push({
        type: 'documentation',
        title: '文档化建议',
        description: '为函数和复杂逻辑添加详细注释',
        example: this.generateDocumentationExample()
      });
    }
  }

  /**
   * 生成文件拆分示例
   * @returns {string} 示例代码
   */
  generateFileSplitExample() {
    return `// 原始大文件 app.js
// 拆分为:
// - utils/lifecycle-manager.js
// - utils/global-data-manager.js
// - utils/api-manager.js

// app.js (重构后)
const LifecycleManager = require('./utils/lifecycle-manager');
const GlobalDataManager = require('./utils/global-data-manager');

App({
  lifecycleManager: new LifecycleManager(),
  globalDataManager: new GlobalDataManager(),
  // 简化的应用逻辑
});`;
  }

  /**
   * 生成模块化示例
   * @returns {string} 示例代码
   */
  generateModularizationExample() {
    return `// 使用类来组织相关函数
class UserManager {
  constructor() {
    this.users = [];
  }
  
  addUser(user) { /* ... */ }
  removeUser(id) { /* ... */ }
  findUser(id) { /* ... */ }
}

// 使用模块导出
module.exports = {
  userManager: new UserManager(),
  apiUtils: require('./api-utils'),
  validators: require('./validators')
};`;
  }

  /**
   * 生成减少嵌套示例
   * @returns {string} 示例代码
   */
  generateNestingReductionExample() {
    return `// 重构前 (嵌套深度过深)
function processUser(user) {
  if (user) {
    if (user.isActive) {
      if (user.permissions) {
        if (user.permissions.canEdit) {
          // 深层嵌套逻辑
        }
      }
    }
  }
}

// 重构后 (早期返回)
function processUser(user) {
  if (!user) return;
  if (!user.isActive) return;
  if (!user.permissions) return;
  if (!user.permissions.canEdit) return;
  
  // 主要逻辑
}`;
  }

  /**
   * 生成文档化示例
   * @returns {string} 示例代码
   */
  generateDocumentationExample() {
    return `/**
 * 用户数据处理函数
 * @param {Object} userData - 用户数据对象
 * @param {string} userData.id - 用户ID
 * @param {string} userData.name - 用户姓名
 * @param {boolean} userData.isActive - 是否激活
 * @returns {Promise<Object>} 处理后的用户数据
 * @throws {Error} 当用户数据无效时抛出错误
 */
async function processUserData(userData) {
  // 验证用户数据
  if (!userData || !userData.id) {
    throw new Error('用户数据无效');
  }
  
  // 处理用户数据
  const processedData = {
    ...userData,
    processedAt: new Date().toISOString()
  };
  
  return processedData;
}`;
  }
}

module.exports = CodeComplexityChecker;
