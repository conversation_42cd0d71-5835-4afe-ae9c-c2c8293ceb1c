/**
 * 错误处理工具函数
 * 专门处理微信小程序常见错误，特别是access_token过期等问题
 */

/**
 * 检查是否是access_token过期错误
 * @param {Error|string} error 错误对象或错误信息
 * @returns {boolean} 是否是access_token过期错误
 */
function isAccessTokenExpiredError(error) {
  const errorMsg = typeof error === 'string' ? error : (error.message || error.errMsg || '');
  return errorMsg.includes('access_token expired') || 
         errorMsg.includes('42001') ||
         errorMsg.includes('getSetting:fail');
}

/**
 * 检查是否是网络错误
 * @param {Error|string} error 错误对象或错误信息
 * @returns {boolean} 是否是网络错误
 */
function isNetworkError(error) {
  const errorMsg = typeof error === 'string' ? error : (error.message || error.errMsg || '');
  return errorMsg.includes('network') || 
         errorMsg.includes('timeout') ||
         errorMsg.includes('Failed to fetch');
}

/**
 * 检查是否是用户取消授权错误
 * @param {Error|string} error 错误对象或错误信息
 * @returns {boolean} 是否是用户取消授权错误
 */
function isUserCancelError(error) {
  const errorMsg = typeof error === 'string' ? error : (error.message || error.errMsg || '');
  return errorMsg.includes('cancel') || 
         errorMsg.includes('deny') ||
         errorMsg.includes('getUserProfile:fail');
}

/**
 * 处理access_token过期错误
 * @param {function} retryCallback 重试回调函数
 * @param {function} cancelCallback 取消回调函数
 */
function handleAccessTokenExpiredError(retryCallback, cancelCallback) {
  console.log('[ErrorHandler] 处理access_token过期错误');
  
  wx.showModal({
    title: '登录状态过期',
    content: '检测到登录状态已过期，这通常是由于以下原因：\n\n1. 微信开发者工具登录过期\n2. 小程序AppID配置问题\n3. 网络连接异常\n\n建议解决方案：\n• 重新登录微信开发者工具\n• 重新编译小程序项目\n• 检查网络连接',
    showCancel: true,
    cancelText: '稍后再试',
    confirmText: '立即重试',
    success: (res) => {
      if (res.confirm && retryCallback) {
        console.log('[ErrorHandler] 用户选择重试');
        retryCallback();
      } else if (res.cancel && cancelCallback) {
        console.log('[ErrorHandler] 用户选择取消');
        cancelCallback();
      }
    }
  });
}

/**
 * 处理网络错误
 * @param {function} retryCallback 重试回调函数
 */
function handleNetworkError(retryCallback) {
  console.log('[ErrorHandler] 处理网络错误');
  
  wx.showModal({
    title: '网络连接失败',
    content: '网络连接异常，请检查网络设置后重试',
    showCancel: true,
    cancelText: '取消',
    confirmText: '重试',
    success: (res) => {
      if (res.confirm && retryCallback) {
        retryCallback();
      }
    }
  });
}

/**
 * 处理用户取消授权错误
 * @param {function} alternativeCallback 替代方案回调函数
 */
function handleUserCancelError(alternativeCallback) {
  console.log('[ErrorHandler] 处理用户取消授权错误');
  
  wx.showModal({
    title: '授权被取消',
    content: '您取消了授权，将无法获取完整的用户信息。是否继续使用基础功能？',
    showCancel: true,
    cancelText: '退出',
    confirmText: '继续',
    success: (res) => {
      if (res.confirm && alternativeCallback) {
        alternativeCallback();
      }
    }
  });
}

/**
 * 通用错误处理函数
 * @param {Error|string} error 错误对象或错误信息
 * @param {Object} options 处理选项
 * @param {function} options.retryCallback 重试回调函数
 * @param {function} options.cancelCallback 取消回调函数
 * @param {function} options.alternativeCallback 替代方案回调函数
 * @param {string} options.defaultTitle 默认错误标题
 * @param {string} options.defaultMessage 默认错误消息
 */
function handleError(error, options = {}) {
  const {
    retryCallback,
    cancelCallback,
    alternativeCallback,
    defaultTitle = '操作失败',
    defaultMessage = '操作过程中发生错误，请重试'
  } = options;
  
  console.log('[ErrorHandler] 处理错误:', error);
  
  // 根据错误类型进行不同处理
  if (isAccessTokenExpiredError(error)) {
    handleAccessTokenExpiredError(retryCallback, cancelCallback);
  } else if (isNetworkError(error)) {
    handleNetworkError(retryCallback);
  } else if (isUserCancelError(error)) {
    handleUserCancelError(alternativeCallback);
  } else {
    // 通用错误处理
    const errorMsg = typeof error === 'string' ? error : (error.message || error.errMsg || defaultMessage);
    
    wx.showModal({
      title: defaultTitle,
      content: errorMsg,
      showCancel: !!retryCallback,
      cancelText: '取消',
      confirmText: retryCallback ? '重试' : '确定',
      success: (res) => {
        if (res.confirm && retryCallback) {
          retryCallback();
        } else if (res.cancel && cancelCallback) {
          cancelCallback();
        }
      }
    });
  }
}

/**
 * 显示成功提示
 * @param {string} message 成功消息
 * @param {number} duration 显示时长（毫秒）
 */
function showSuccess(message, duration = 2000) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: duration
  });
}

/**
 * 显示警告提示
 * @param {string} message 警告消息
 * @param {number} duration 显示时长（毫秒）
 */
function showWarning(message, duration = 2000) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: duration
  });
}

/**
 * 显示加载提示
 * @param {string} message 加载消息
 * @param {boolean} mask 是否显示透明蒙层
 */
function showLoading(message = '加载中...', mask = true) {
  wx.showLoading({
    title: message,
    mask: mask
  });
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 记录错误日志
 * @param {string} module 模块名称
 * @param {string} action 操作名称
 * @param {Error|string} error 错误信息
 * @param {Object} context 上下文信息
 */
function logError(module, action, error, context = {}) {
  const errorInfo = {
    module: module,
    action: action,
    error: typeof error === 'string' ? error : {
      message: error.message,
      errMsg: error.errMsg,
      stack: error.stack
    },
    context: context,
    timestamp: new Date().toISOString(),
    userAgent: wx.getSystemInfoSync()
  };
  
  console.error(`[${module}] ${action} 错误:`, errorInfo);
  
  // 可以在这里添加错误上报逻辑
  // 例如：上报到云开发数据库或第三方错误监控服务
}

module.exports = {
  isAccessTokenExpiredError,
  isNetworkError,
  isUserCancelError,
  handleAccessTokenExpiredError,
  handleNetworkError,
  handleUserCancelError,
  handleError,
  showSuccess,
  showWarning,
  showLoading,
  hideLoading,
  logError
};
