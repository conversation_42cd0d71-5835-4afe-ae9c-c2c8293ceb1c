/* pages/community/community.wxss */

/* Global Styles */
page {
    background-color: #F0F2F5; /* A light grey background for better contrast */
}

.feed-container {
    padding: 16rpx; /* 减少外边距，使布局更紧凑 */
    display: flex;
    flex-direction: column;
    gap: 16rpx; /* 减少卡片间距，使布局更紧凑 */
}

/* Card Styles */
.post-card {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    border: 1rpx solid #E0E0E0;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* Softer, more modern shadow */
    overflow: hidden; /* Ensures content respects the border radius */
    display: flex;
    flex-direction: column;
}

/* User Info Header */
.card-header {
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx; /* 减少内边距，使布局更紧凑 */
}
.user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
}
.user-name {
    font-weight: 600;
    font-size: 32rpx; /* 保持H3标准 */
}

/* Main Content Image */
.card-image {
    width: 100%;
    height: auto; /* Let mode="widthFix" handle the aspect ratio */
}

/* Content & Actions Section */
.card-content {
    padding: 16rpx 24rpx; /* 减少内边距，使布局更紧凑 */
}
.content-text {
    font-size: 30rpx; /* 统一字体：调整为标准正文大小 */
    line-height: 1.5; /* 减少行高，使布局更紧凑 */
    margin-bottom: 16rpx; /* 减少下边距，使布局更紧凑 */
    display: block; /* Make it a block-level element */
}

/* Location Tag */
.location-tag {
    display: inline-flex;
    align-items: center;
    background-color: #F0F2F5;
    border-radius: 32rpx;
    padding: 8rpx 16rpx; /* 减少内边距，使布局更紧凑 */
    font-size: 24rpx; /* 保持提示文字标准 */
    color: #555;
    margin-bottom: 16rpx; /* 减少下边距，使布局更紧凑 */
}
.location-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
}

/* Action Buttons Footer */
.card-actions {
    display: flex;
    justify-content: space-around;
    border-top: 1rpx solid #F0F2F5;
    padding: 12rpx 0; /* 减少内边距，使布局更紧凑 */
}
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 12rpx; /* 减少内边距，使布局更紧凑 */
    gap: 12rpx; /* 减少间距，使布局更紧凑 */
    font-size: 26rpx; /* 保持说明文字标准 */
    color: #606770;
}

.action-icon {
    width: 40rpx;
    height: 40rpx;
}

/* 外卖商铺推荐区域样式 */
.shop-section {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 20rpx; /* 减少内边距，使布局更紧凑 */
    margin-bottom: 16rpx; /* 减少下边距，使布局更紧凑 */
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx; /* 减少下边距，使布局更紧凑 */
}

.section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

.more-btn {
    font-size: 28rpx;
    color: #FF6B35;
    font-weight: 500;
}

.shop-scroll {
    white-space: nowrap;
}

.shop-card {
    display: inline-block;
    width: 280rpx;
    margin-right: 24rpx;
    background-color: #F8F9FA;
    border-radius: 16rpx;
    overflow: hidden;
    vertical-align: top;
}

.shop-image {
    width: 100%;
    height: 160rpx;
}

.shop-info {
    padding: 16rpx;
}

.shop-name {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 8rpx;
}

.shop-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8rpx;
}

.shop-rating {
    font-size: 24rpx;
    color: #FF6B35;
}

.shop-distance {
    font-size: 24rpx;
    color: #666;
}

.shop-desc {
    font-size: 24rpx;
    color: #999;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 分类导航样式 */
.category-section {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 20rpx; /* 减少内边距，使布局更紧凑 */
    margin-bottom: 16rpx; /* 减少下边距，使布局更紧凑 */
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx; /* 减少网格间距，使布局更紧凑 */
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16rpx; /* 减少内边距，使布局更紧凑 */
    border-radius: 16rpx;
    background-color: #F8F9FA;
    transition: all 0.3s ease;
}

.category-item:active {
    background-color: #E9ECEF;
    transform: scale(0.95);
}

.category-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 12rpx;
}

.category-name {
    font-size: 24rpx;
    color: #333;
    text-align: center;
}

/* 热门话题样式 */
.topic-section {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 20rpx; /* 减少内边距，使布局更紧凑 */
    margin-bottom: 16rpx; /* 减少下边距，使布局更紧凑 */
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.topic-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx; /* 减少话题标签间距，使布局更紧凑 */
}

.topic-tag {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
    border-radius: 32rpx;
    padding: 16rpx 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120rpx;
    transition: all 0.3s ease;
}

.topic-tag:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.topic-text {
    font-size: 26rpx;
    color: #FFFFFF;
    font-weight: 600;
    margin-bottom: 4rpx;
}

.topic-count {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.8);
}

/* 标题栏样式 */
.title-bar {
    background-color: #FFFFFF;
    padding: 16rpx 24rpx; /* 减少内边距，使布局更紧凑 */
    border-bottom: 1rpx solid #E0E0E0;
    margin-bottom: 8rpx; /* 减少下边距，使布局更紧凑 */
    border-radius: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.title {
    font-size: 40rpx; /* 统一字体：调整为H1标准 */
    font-weight: 600;
    color: #333;
    text-align: center;
    display: block;
}

/* 浮动发布按钮样式 (缩小并半透明) */
.floating-publish-btn {
    position: fixed;
    bottom: 100rpx;
    right: 40rpx;
    width: 100rpx; /* 缩小尺寸 */
    height: 100rpx; /* 缩小尺寸 */
    background-color: #000000;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
    z-index: 999;
    opacity: 0.75; /* 默认半透明 */
    transform: scale(1); /* 添加 transform 以便过渡 */
    transition: all 0.3s ease;
}

.floating-publish-btn:active {
    transform: scale(1.05); /* 按下时稍微放大 */
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
    opacity: 1; /* 按下时完全不透明 */
}

.publish-icon {
    width: 40rpx; /* 按比例缩小图标 */
    height: 40rpx; /* 按比例缩小图标 */
    margin-bottom: 4rpx;
    filter: brightness(0) invert(1); /* 将图标变为白色 */
}

.publish-text {
    font-size: 18rpx; /* 按比例缩小文字 */
    color: #FFFFFF;
    font-weight: 500;
}

/* 商铺推荐区域样式 - Shop Recommendation Styles */
.shop-section {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
}

.more-btn {
    font-size: 28rpx;
    color: #FF6B35;
    font-weight: 500;
}

.shop-list {
    white-space: nowrap;
}

.shop-item {
    display: inline-block;
    width: 280rpx;
    margin-right: 16rpx;
    background-color: #F8F9FA;
    border-radius: 16rpx;
    overflow: hidden;
    vertical-align: top;
}

.shop-image {
    width: 100%;
    height: 160rpx;
}

.shop-info {
    padding: 16rpx;
}

.shop-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    display: block;
    margin-bottom: 8rpx;
}

.shop-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8rpx;
}

.shop-rating {
    font-size: 24rpx;
    color: #FF6B35;
}

.shop-distance {
    font-size: 24rpx;
    color: #666666;
}

.shop-desc {
    font-size: 24rpx;
    color: #999999;
    display: block;
}

/* 分类导航样式 - Category Navigation Styles */
.category-section {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.category-list {
    white-space: nowrap;
}

.category-item {
    display: inline-block;
    text-align: center;
    margin-right: 32rpx;
    vertical-align: top;
}

.category-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 8rpx;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.category-name {
    font-size: 24rpx;
    color: #666666;
    display: block;
}

/* 热门话题样式 - Hot Topics Styles */
.topic-section {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.topic-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
}

.topic-item {
    display: flex;
    align-items: center;
    background-color: #F8F9FA;
    border-radius: 32rpx;
    padding: 12rpx 20rpx;
    border: 1rpx solid #E0E0E0;
}

.topic-name {
    font-size: 26rpx;
    color: #FF6B35;
    font-weight: 500;
    margin-right: 8rpx;
}

.topic-count {
    font-size: 24rpx;
    color: #999999;
}

/* 多图显示样式 - Multiple Images Styles */
.card-images {
    margin-bottom: 16rpx;
}

.images-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    padding: 0 24rpx;
}

.grid-image {
    width: calc(33.33% - 6rpx);
    height: 200rpx;
    border-radius: 12rpx;
}

/* 当只有1张图片时 */
.images-grid .grid-image:only-child {
    width: 100%;
    height: 400rpx;
}

/* 当有2张图片时 - When there are 2 images */
.images-grid .grid-image:first-child:nth-last-child(2),
.images-grid .grid-image:nth-child(2):last-child {
    width: calc(50% - 4rpx);
    height: 300rpx;
}

/* 时间显示样式 - Time Display Styles */
.post-time {
    font-size: 24rpx;
    color: #999999;
    margin-left: auto;
}

/* 加载状态样式 - Loading State Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 错误状态样式 - Error State Styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.retry-btn:active {
  background: #ff5252;
  transform: translateY(2rpx);
}
