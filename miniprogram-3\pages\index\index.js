// pages/index/index.js
Page({
  data: {
    cards: [], // 卡片数组
    selectedFood: null, // 最终选中的食物
    canConfirm: false, // 是否可以点击"就选这个"按钮
    isConfirmed: false, // 是否已确认最终选择，用于控制弹窗显示
    // 用户偏好设置
    preferences: {
      want: false,    // 想吃的
      unwant: false,  // 不想吃的
      can: false,     // 能吃的
      cannot: false   // 不能吃的
    },
    // 内置的食物数据，符合极简设计，不需要图片
    mockFoods: [
      { id: 1, name: '麻辣小龙虾' },
      { id: 2, name: '日式寿喜锅' },
      { id: 3, name: '韩式炸鸡' },
      { id: 4, name: '广式早茶' },
      { id: 5, 'name': '北京烤鸭' },
      { id: 6, name: '四川火锅' },
      { id: 7, name: '兰州拉面' },
      { id: 8, name: '美味汉堡' },
      { id: 9, name: '海鲜披萨' },
      { id: 10, name: '螺蛳粉' },
      { id: 11, name: '沙县小吃' },
      { id: 12, name: '黄焖鸡米饭' },
    ]
  },

  onLoad: function () {
    this.initGame();
  },

  /**
   * @description 页面显示时重置游戏状态
   * 当用户从结果页面返回首页时，需要重置卡片状态
   */
  onShow: function () {
    // 如果页面已经确认过选择，说明用户是从结果页面返回的
    if (this.data.isConfirmed) {
      console.log('从结果页面返回，重置游戏状态');
      this.resetGame();
    }
  },

  /**
   * @description 初始化或重置游戏
   */
  initGame() {
    const shuffledFoods = this.shuffle([...this.data.mockFoods]);
    const cards = Array(9).fill(null).map((_, index) => ({
      id: index,
      name: shuffledFoods[index].name,
      isFlipped: false, // 是否翻开
      isSelected: false, // 是否被选中（翻开后即选中）
    }));

    this.setData({
      cards,
      selectedFood: null,
      canConfirm: false,
      isConfirmed: false,
    });
  },

  /**
   * @description Fisher-Yates 洗牌算法，打乱数组顺序
   * @param {Array} array 需要打乱的数组
   * @returns {Array} 打乱后的数组
   */
  shuffle(array) {
    let currentIndex = array.length, randomIndex;
    while (currentIndex != 0) {
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex--;
      [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
    }
    return array;
  },

  /**
   * @description 用户点击卡片进行选择（不立即翻转）
   * @param {Object} e 事件对象
   */
  flipCard(e) {
    // 如果已经有卡片被选中，或者已经确认了结果，则不允许再选择
    if (this.data.canConfirm || this.data.isConfirmed) {
      wx.showToast({
        title: '请先确认或重置',
        icon: 'none'
      });
      return;
    }

    const index = e.currentTarget.dataset.index;
    const selectedCard = this.data.cards[index];

    // 只标记为选中状态，不翻转卡片
    this.setData({
      [`cards[${index}].isSelected`]: true,
      selectedFood: selectedCard, // 记录选中的食物
      canConfirm: true, // 用户现在可以点击"就选这个"按钮了
    });
  },

  /**
   * @description 用户确认选择，执行翻转动画并跳转到结果页面
   */
  confirmSelection() {
    if (!this.data.canConfirm) return;

    // 找到被选中的卡片，执行翻转并标记为已确认
    const cards = this.data.cards.map(card => {
      if (card.isSelected) {
        return { ...card, isFlipped: true, isConfirmed: true };
      }
      return card;
    });

    // 更新数据，显示结果弹窗
    this.setData({
      cards,
      isConfirmed: true, // 标记已确认，用于显示弹窗和锁定盘面
    });

    // 延迟跳转到结果页面，让用户看到翻转动画
    setTimeout(() => {
      const selectedFood = this.data.selectedFood;
      console.log('准备跳转到结果页面，选中的食物：', selectedFood);
      
      if (selectedFood) {
        // 将选中的食物数据存储到全局数据中
        const app = getApp();
        app.globalData.lastResult = selectedFood.name;
        app.globalData.currentResult = selectedFood;
        console.log('已将食物数据存储到全局：', selectedFood.name);
        
        // 跳转到结果页面（tabBar页面使用switchTab）
        wx.switchTab({
          url: '/pages/result/result',
          success: function(res) {
            console.log('页面跳转成功', res);
          },
          fail: function(err) {
            console.error('页面跳转失败', err);
          }
        });
      } else {
        console.error('没有选中的食物，无法跳转');
      }
    }, 1000); // 等待1秒让用户看到翻转效果
  },

  /**
   * @description 关闭结果弹窗
   */
  closeModal() {
    // 关闭弹窗，但保持页面的确认状态，用户只能点击“再来一次”
    this.setData({ isConfirmed: false }); 
    this.resetGame(); // 或者直接重置游戏
  },

  /**
   * @description 重置游戏，先重置翻转状态再更新内容
   */
  resetGame() {
    // 第一步：先重置所有卡片的翻转和选中状态，保持原有内容
    const resetCards = this.data.cards.map(card => ({
      ...card,
      isFlipped: false,
      isSelected: false,
      isConfirmed: false
    }));
    
    this.setData({
      cards: resetCards,
      selectedFood: null,
      canConfirm: false,
      isConfirmed: false
    });
    
    // 第二步：延迟更新卡片内容，避免翻转过程中内容闪烁
    setTimeout(() => {
      this.initGame();
    }, 100); // 等待翻转动画完成
  },

  /**
   * @description 切换偏好设置
   * @param {Object} e 事件对象
   */
  togglePreference(e) {
    const type = e.currentTarget.dataset.type;
    const currentValue = this.data.preferences[type];
    
    this.setData({
      [`preferences.${type}`]: !currentValue
    });
    
    console.log('偏好设置已更新:', this.data.preferences);
  },

  /**
   * @description 跳转到偏好设置页面
   */
  goToPreferences() {
    wx.navigateTo({
      url: '/pages/preferences/preferences'
    });
  },

  /**
   * @description 分享功能
   */
  onShare() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * @description 页面分享配置
   */
  onShareAppMessage() {
    return {
      title: '今天吃什么？一起来决定吧！',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png'
    };
  }
});
