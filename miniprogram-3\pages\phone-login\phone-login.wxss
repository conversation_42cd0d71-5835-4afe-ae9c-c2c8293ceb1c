/* pages/phone-login/phone-login.wxss */

.container {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f8f8;
}

.back-icon {
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.placeholder {
  width: 80rpx;
}

/* 登录表单 */
.login-form {
  flex: 1;
  padding: 80rpx 60rpx 40rpx;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 100rpx;
}

.logo {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.welcome-text {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #999999;
}

/* 输入组 */
.input-group {
  margin-bottom: 60rpx;
}

.input-label {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  border-bottom: 2rpx solid #e0e0e0;
  padding-bottom: 20rpx;
}

.input-wrapper.code-wrapper {
  display: flex;
  align-items: center;
}

.phone-input,
.code-input {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
}

.code-input {
  flex: 1;
}

.code-btn {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  border: none;
  margin: 0;
  padding: 0;
}

.code-btn.active {
  background: #007aff;
  color: #ffffff;
}

.code-btn.disabled {
  background: #f0f0f0;
  color: #cccccc;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 36rpx;
  font-weight: 600;
  border-radius: 50rpx;
  border: none;
  margin: 80rpx 0 60rpx;
  padding: 0;
}

.login-btn.active {
  background: #007aff;
  color: #ffffff;
}

.login-btn.disabled {
  background: #f0f0f0;
  color: #cccccc;
}

/* 提示信息 */
.tips {
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}

.link {
  color: #007aff;
}

/* 其他登录方式 */
.other-login {
  padding: 40rpx 60rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 60rpx;
}

.line {
  flex: 1;
  height: 1rpx;
  background: #e0e0e0;
}

.divider-text {
  font-size: 24rpx;
  color: #999999;
  margin: 0 40rpx;
}

.login-methods {
  display: flex;
  justify-content: center;
}

.method-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 40rpx;
}

.method-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.method-icon.wechat {
  background: #1aad19;
}

.method-text {
  font-size: 24rpx;
  color: #666666;
}

/* 输入框聚焦状态 */
.input-wrapper:focus-within {
  border-bottom-color: #007aff;
}

/* 按钮点击效果 */
.back-btn:active {
  background: #e0e0e0;
}

.code-btn.active:active {
  background: #0056cc;
}

.login-btn.active:active {
  background: #0056cc;
}

.method-item:active .method-icon.wechat {
  background: #179b16;
}
