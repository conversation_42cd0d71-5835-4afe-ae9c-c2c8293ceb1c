{"description": "云函数配置文件", "cloudfunctionRoot": "cloudfunctions/", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "appid": "wxd647e87260152f82", "projectname": "今天吃什么小程序-云函数", "libVersion": "3.8.11", "cloudfunctions": {"auth": {"handler": "index.main", "timeout": 20, "envVariables": {}, "runtime": "Nodejs18.15"}, "database": {"handler": "index.main", "timeout": 20, "envVariables": {}, "runtime": "Nodejs18.15"}}}