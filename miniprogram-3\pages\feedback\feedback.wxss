/* pages/feedback/feedback.wxss */
/* 建议反馈页面样式 */

.feedback-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left {
  display: flex;
  align-items: center;
  width: 80rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.nav-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.history-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 快捷入口 */
.quick-actions {
  display: flex;
  padding: 24rpx 32rpx;
  gap: 24rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.98);
  background-color: #f8f8f8;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

/* 反馈类型 */
.feedback-types {
  padding: 0 32rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.char-count {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
}

.optional-text {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.type-item.selected {
  border-color: #000000;
  background-color: #f8f9ff;
}

.type-item:active {
  transform: scale(0.98);
}

.type-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  border: 2rpx solid;
  transition: all 0.3s ease;
}

.type-item.selected .type-icon {
  background-color: #000000 !important;
}

.icon-image {
  width: 40rpx;
  height: 40rpx;
  transition: filter 0.3s ease;
}

.type-name {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  text-align: center;
}

.type-check {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #000000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 16rpx;
  height: 16rpx;
  filter: brightness(0) invert(1);
}

/* 表单区域 */
.feedback-form {
  padding: 0 32rpx;
}

.form-section {
  margin-bottom: 32rpx;
}

.input-wrapper,
.textarea-wrapper {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.title-input,
.contact-input {
  width: 100%;
  font-size: 30rpx;
  color: #000000;
  line-height: 1.5;
}

.title-input::placeholder,
.contact-input::placeholder {
  color: #999999;
}

.content-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 30rpx;
  color: #000000;
  line-height: 1.6;
}

.content-textarea::placeholder {
  color: #999999;
}

/* 图片上传 */
.image-upload {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  width: 16rpx;
  height: 16rpx;
  filter: brightness(0) invert(1);
}

.add-image-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.add-image-btn:active {
  background-color: #f0f0f0;
  border-color: #bfbfbf;
}

.add-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #999999;
}

/* 优先级选择 */
.priority-options {
  display: flex;
  gap: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.priority-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.priority-item.selected {
  border-color: #000000;
  background-color: #f8f9ff;
}

.priority-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.priority-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

/* 其他选项 */
.option-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-child {
  border-bottom: none;
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 8rpx;
  display: block;
}

.option-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.option-switch {
  transform: scale(0.8);
}

/* 提交区域 */
.submit-section {
  padding: 32rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #000000;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  position: relative;
  transition: all 0.3s ease;
}

.submit-btn:disabled {
  background-color: #d9d9d9;
}

.submit-btn.submitting {
  background-color: #666666;
}

.submit-btn:not(:disabled):active {
  transform: scale(0.98);
  background-color: #333333;
}

.submit-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}

.loading-icon {
  margin-left: 16rpx;
}

.loading-image {
  width: 32rpx;
  height: 32rpx;
}

.submit-tips {
  text-align: center;
  margin-top: 24rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.4;
}

/* 模态框通用样式 */
.history-modal,
.faq-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.history-content,
.faq-content {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

.history-header,
.faq-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-title,
.faq-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.history-close,
.faq-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 历史反馈列表 */
.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 32rpx 32rpx;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  flex: 1;
}

.history-type {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.history-title-text {
  font-size: 28rpx;
  color: #000000;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: 24rpx;
  color: #999999;
}

.history-status {
  margin-left: 16rpx;
}

.status-text {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-pending {
  color: #666666; /* 改为灰色 */
  background-color: #f5f5f5; /* 改为浅灰色背景 */
}

.status-processing {
  color: #333333; /* 改为深灰色 */
  background-color: #f0f0f0; /* 改为浅灰色背景 */
}

.status-resolved {
  color: #000000; /* 改为黑色 */
  background-color: #e8e8e8; /* 改为浅灰色背景 */
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* 常见问题列表 */
.faq-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 32rpx;
}

.faq-item {
  margin-bottom: 32rpx;
}

.faq-question {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.question-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

.question-text {
  font-size: 30rpx;
  color: #000000;
  font-weight: 500;
  line-height: 1.5;
}

.faq-answer {
  padding-left: 48rpx;
}

.answer-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.faq-footer {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #f8f9fa;
}

.footer-text {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
  text-align: center;
}

/* 底部安全区域 */
.safe-area {
  height: env(safe-area-inset-bottom);
  background-color: #f8f9fa;
}

/* 深色模式适配预留 */
@media (prefers-color-scheme: dark) {
  .feedback-container {
    background-color: #000000;
  }
  
  .nav-header {
    background-color: #1a1a1a;
    border-bottom-color: #333333;
  }
  
  .nav-title {
    color: #ffffff;
  }
  
  .section-title {
    color: #ffffff;
  }
  
  .action-item,
  .input-wrapper,
  .textarea-wrapper,
  .image-upload,
  .priority-options,
  .option-list {
    background-color: #1a1a1a;
  }
  
  .safe-area {
    background-color: #000000;
  }
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .nav-header {
    padding: 0 24rpx;
  }
  
  .quick-actions,
  .feedback-types,
  .feedback-form,
  .submit-section {
    padding-left: 24rpx;
    padding-right: 24rpx;
  }
  
  .type-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.type-item,
.form-section {
  animation: fadeIn 0.3s ease;
}

.history-content,
.faq-content {
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-image {
  animation: spin 1s linear infinite;
}