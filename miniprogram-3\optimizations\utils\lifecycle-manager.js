/**
 * 应用生命周期管理器
 * 统一管理应用的生命周期事件
 */
class LifecycleManager {
  constructor() {
    this.listeners = {
      onLaunch: [],
      onShow: [],
      onHide: [],
      onError: []
    };
  }

  /**
   * 注册生命周期监听器
   * @param {string} event 事件名称
   * @param {Function} handler 处理函数
   */
  on(event, handler) {
    if (this.listeners[event]) {
      this.listeners[event].push(handler);
    }
  }

  /**
   * 触发生命周期事件
   * @param {string} event 事件名称
   * @param {*} data 事件数据
   */
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('生命周期事件处理错误 [' + event + ']:', error);
        }
      });
    }
  }

  /**
   * 应用启动处理
   * @param {Object} options 启动参数
   */
  handleLaunch(options) {
    console.log('应用启动', options);
    
    // 初始化全局数据
    this.initGlobalData();
    
    // 初始化云开发
    this.initCloud();
    
    // 触发自定义启动事件
    this.emit('onLaunch', options);
  }

  /**
   * 应用显示处理
   * @param {Object} options 显示参数
   */
  handleShow(options) {
    console.log('应用显示', options);
    
    // 更新应用状态
    this.updateAppStatus('active');
    
    // 触发自定义显示事件
    this.emit('onShow', options);
  }

  /**
   * 应用隐藏处理
   */
  handleHide() {
    console.log('应用隐藏');
    
    // 更新应用状态
    this.updateAppStatus('background');
    
    // 触发自定义隐藏事件
    this.emit('onHide');
  }

  /**
   * 应用错误处理
   * @param {string} error 错误信息
   */
  handleError(error) {
    console.error('应用错误:', error);
    
    // 错误上报
    this.reportError(error);
    
    // 触发自定义错误事件
    this.emit('onError', error);
  }

  /**
   * 初始化全局数据
   */
  initGlobalData() {
    const app = getApp();
    if (app) {
      app.globalData = {
        userInfo: null,
        isLoggedIn: false,
        appStatus: 'launching',
        version: '1.0.0',
        ...app.globalData
      };
    }
  }

  /**
   * 初始化云开发
   */
  initCloud() {
    if (wx.cloud) {
      wx.cloud.init({
        env: 'your-env-id', // 替换为实际的环境ID
        traceUser: true
      });
      console.log('云开发初始化成功');
    }
  }

  /**
   * 更新应用状态
   * @param {string} status 应用状态
   */
  updateAppStatus(status) {
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.appStatus = status;
    }
  }

  /**
   * 错误上报
   * @param {string} error 错误信息
   */
  reportError(error) {
    // 这里可以添加错误上报逻辑
    // 例如上报到云函数或第三方服务
    console.log('错误上报:', error);
  }
}

// 导出生命周期管理器
module.exports = LifecycleManager;
