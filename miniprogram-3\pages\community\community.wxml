<!-- pages/community/community.wxml -->
<view class="feed-container">
    <!-- 顶部标题栏 -->
    <view class="title-bar" style="position: relative; left: 0rpx; top: -32rpx">
        <text class="title">美食社区</text>
    </view>

    <!-- 外卖商铺推荐区域 -->
    <view class="shop-section">
        <view class="section-header">
            <text class="section-title">附近商铺</text>
            <text class="more-btn" bindtap="viewMoreShops">更多 ></text>
        </view>
        <scroll-view class="shop-list" scroll-x="true">
            <view class="shop-item" wx:for="{{shopList}}" wx:key="shopId" bindtap="goToShop" data-shop="{{item}}">
                <image src="{{item.coverImage}}" class="shop-image" mode="aspectFill"/>
                <view class="shop-info">
                    <text class="shop-name">{{item.name}}</text>
                    <view class="shop-meta">
                        <text class="shop-rating">⭐ {{item.rating}}</text>
                        <text class="shop-distance">{{item.distance}}</text>
                    </view>
                    <text class="shop-desc">{{item.description}}</text>
                </view>
            </view>
        </scroll-view>
    </view>

    <!-- 分类导航 -->
    

    <!-- 热门话题 -->
    <view class="topic-section">
        <view class="section-header">
            <text class="section-title">热门话题</text>
        </view>
        <view class="topic-list">
            <view class="topic-item" wx:for="{{topicList}}" wx:key="id" bindtap="selectTopic" data-topic="{{item.id}}">
                <text class="topic-name">#{{item.name}}</text>
                <text class="topic-count">{{item.count}}</text>
            </view>
        </view>
    </view>

    <!-- 加载状态 - Loading State -->
    <view wx:if="{{isLoading}}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 - Error State -->
    <view wx:elif="{{hasError}}" class="error-container">
        <view class="error-icon">⚠️</view>
        <text class="error-message">{{errorMessage}}</text>
        <button class="retry-btn" bindtap="onRetry">重试</button>
    </view>

    <!-- Post Card List -->
    <block wx:for="{{feedList}}" wx:key="postId">
        <view class="post-card">
            <!-- User Info Header -->
            <view class="card-header">
                <image src="{{item.user.avatarUrl}}" alt="User Avatar" class="user-avatar"/>
                <text class="user-name">{{item.user.userName}}</text>
                <text class="post-time">{{item.createTime}}</text>
            </view>

            <!-- Content & Actions Section - 内容文字区域 -->
            <view class="card-content">
                <text class="content-text">{{item.content}}</text>
                
                <!-- Location Tag -->
                <view class="location-tag" wx:if="{{item.location}}" bindtap="handleLocationClick" data-location="{{item.location}}">
                    <image src="/images/icon_location.svg" alt="Location" class="location-icon"/>
                    <text>{{item.location.storeName || item.location.name}}</text>
                </view>
            </view>

            <!-- Main Content Images - 图片显示区域 -->
            <view class="card-images" wx:if="{{item.images && item.images.length > 0}}">
                <!-- 单张图片显示 -->
                <image wx:if="{{item.images.length === 1}}" src="{{item.images[0]}}" alt="Food Image" class="card-image" mode="widthFix"/>
                <!-- 多张图片显示 -->
                <view wx:elif="{{item.images.length > 1}}" class="images-grid">
                    <image wx:for="{{item.images}}" wx:for-item="img" wx:key="*this" src="{{img}}" class="grid-image" mode="aspectFill" bindtap="onPreviewImage" data-images="{{item.images}}" data-current="{{img}}"/>
                </view>
            </view>
            <!-- 兼容旧数据格式的单图显示 -->
            <view class="card-images" wx:elif="{{item.imageUrl}}">
                <image src="{{item.imageUrl}}" alt="Food Image" class="card-image" mode="widthFix"/>
            </view>

            <!-- Action Buttons Footer -->
            <view class="card-actions">
                <view class="action-button" bindtap="toggleLike" data-index="{{index}}">
                    <image src="{{item.isLiked ? '/images/icon_liked.svg' : '/images/icon_like.svg'}}" alt="Like" class="action-icon"/>
                    <text>{{item.likes}}</text>
                </view>
                <view class="action-button" bindtap="handleComment" data-post-id="{{item.postId || item.id}}">
                    <image src="/images/icon_comment.svg" alt="Comment" class="action-icon"/>
                    <text>{{item.comments}}</text>
                </view>
                <view class="action-button" bindtap="handleShare" data-post-id="{{item.postId || item.id}}">
                    <image src="/images/icon_share.svg" alt="Share" class="action-icon"/>
                    <text>{{item.shares}}</text>
                </view>
                <view class="action-button" bindtap="toggleCollect" data-index="{{index}}">
                    <image src="{{item.isCollected ? '/images/icon_collected.svg' : '/images/icon_collect.svg'}}" alt="Collect" class="action-icon"/>
                    <text>{{item.collects || 0}}</text>
                </view>
            </view>
        </view>
    </block>

    <!-- 浮动发布按钮 -->
    <view class="floating-publish-btn" bindtap="navigateToPublish">
        <image src="/images/publish.svg" alt="发布" class="publish-icon"/>
        <text class="publish-text">发布</text>
    </view>
</view>