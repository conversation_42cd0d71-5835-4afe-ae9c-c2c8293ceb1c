/* pages/index/index.wxss */
/* 首页样式 - 严格执行白底黑字黑框风格 (最终修订版) */

/* 容器和基础布局 */
.container {
  background-color: #ffffff; /* 白底 */
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color: #000000; /* 黑字 */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-header {
  background-color: #ffffff; /* 白底 */
  border-bottom: 4rpx solid #000000; /* 黑框 */
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left {
  flex: 1;
}

.nav-title {
  font-size: 40rpx; /* 统一字体：调整为H1标准 */
  font-weight: 700;
  color: #000000; /* 黑字 */
  letter-spacing: 1rpx;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  background-color: #ffffff; /* 白底 */
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40rpx 30rpx;
}

.page-subtitle {
  font-size: 30rpx; /* 统一字体：调整为标准正文大小 */
  color: #000000; /* 黑字 */
  text-align: center;
  margin-bottom: 40rpx;
}

/* 翻牌网格样式 */
.flip-grid-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.flip-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  perspective: 1000px;
  background-color: #ffffff; /* 确保网格背景为白色 */
}

.flip-card {
  width: 100%;
  aspect-ratio: 1 / 1;
  background-color: transparent;
  cursor: pointer;
  transition: box-shadow 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.flip-card.is-selected {
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2); /* 减弱阴影，作为辅助 */
  transform: scale(1.05) translateY(-5rpx); /* 放大并向上浮动 */
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.7s;
  transform-style: preserve-3d;
}

.flip-card-inner.is-flipped {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #000000; /* 黑框 */
  border-radius: 16rpx;
  box-shadow: none; /* 移除阴影 */
}

.flip-card-front {
  background-color: #ffffff; /* 白底 */
  font-size: 64rpx;
  font-weight: bold;
  color: #000000; /* 黑字 */
}

.flip-card-back {
  background-color: #ffffff; /* 白底 */
  color: #000000; /* 黑字 */
  transform: rotateY(180deg);
  font-size: 36rpx;
  font-weight: bold;
  padding: 10rpx;
}

/* 操作按钮区域样式 */
.action-buttons {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.button {
  display: block;
  width: 100%;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  color: #000000; /* 黑字 */
  background-color: #ffffff; /* 白底 */
  border: 2rpx solid #000000; /* 黑框 */
  border-radius: 16rpx;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  box-shadow: none; /* 移除阴影 */
}

.button:active {
  background-color: #f0f0f0; /* 轻微的点击效果 */
}

.button.primary {
  background-color: #ffffff; /* 白底 */
  color: #000000; /* 黑字 */
}

.button.primary:active {
    background-color: #f0f0f0; /* 轻微的点击效果 */
}
