/* pages/favorites/favorites.wxss */
/* 收藏夹页面样式 */

/* 全局样式 */
.favorites-container {
  min-height: 100vh;
  background-color: #ffffff;
  color: #000000;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-action {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.nav-action:active {
  background-color: #f5f5f5;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.8;
}

/* 搜索栏 */
.search-bar {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
  background: transparent;
}

.search-clear {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 统计栏 */
.stats-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #fafafa;
}

.stats-text {
  font-size: 26rpx;
  color: #666666;
}

.action-btn {
  padding: 12rpx 20rpx;
  background-color: #000000;
  border-radius: 8rpx;
}

.action-text {
  font-size: 26rpx;
  color: #ffffff;
}

/* 标签页 */
.tabs-container {
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs {
  display: flex;
  padding: 0 32rpx;
}

.tab-item {
  flex-shrink: 0;
  padding: 24rpx 32rpx;
  margin-right: 16rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.tab-item.active {
  background-color: #000000;
}

.tab-text {
  font-size: 28rpx;
  color: #666666;
  transition: color 0.2s ease;
}

.tab-item.active .tab-text {
  color: #ffffff;
  font-weight: 500;
}

/* 编辑工具栏 */
.edit-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #e8e8e8;
}

.select-all {
  padding: 12rpx 20rpx;
  background-color: #ffffff;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
}

.select-text {
  font-size: 26rpx;
  color: #000000;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}

.toolbar-btn.delete {
  background-color: #ff4d4f;
}

.btn-icon {
  width: 28rpx;
  height: 28rpx;
}

.btn-text {
  font-size: 26rpx;
  color: #ffffff;
}

/* 收藏列表 */
.favorites-list {
  flex: 1;
  background-color: #ffffff;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #000000;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 收藏项 */
.favorite-items {
  padding: 0 32rpx;
}

.favorite-item {
  position: relative;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.favorite-item.edit-mode {
  padding-left: 80rpx;
}

.favorite-item.selected {
  border-color: #000000;
  background-color: #fafafa;
}

.favorite-item:active {
  transform: scale(0.98);
}

/* 选择框 */
.select-checkbox {
  position: absolute;
  left: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background-color: #000000;
  border-color: #000000;
}

.check-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox.checked .check-icon {
  opacity: 1;
}

/* 美食收藏样式 */
.food-favorite {
  display: flex;
  gap: 24rpx;
}

.food-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.food-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.food-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  line-height: 1.3;
}

.food-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.food-meta {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.food-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
}

.rating-text {
  font-size: 26rpx;
  color: #ff9500;
  font-weight: 500;
}

.food-time {
  font-size: 24rpx;
  color: #999999;
}

.food-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  padding: 6rpx 16rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
}

.tag-text {
  font-size: 22rpx;
  color: #666666;
}

/* 动态收藏样式 */
.post-favorite {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.post-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.author-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 4rpx;
}

.post-time {
  font-size: 24rpx;
  color: #999999;
}

.post-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.post-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  line-height: 1.4;
}

.post-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-images {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.post-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.post-stats {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.6;
}

.stat-text {
  font-size: 26rpx;
  color: #666666;
}

/* 用户收藏样式 */
.user-favorite {
  display: flex;
  gap: 24rpx;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.name-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.verified-icon {
  width: 28rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 50%;
  padding: 4rpx;
}

.user-bio {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.user-stats {
  display: flex;
  gap: 32rpx;
}

.user-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
}

.stat-label {
  font-size: 22rpx;
  color: #999999;
}

/* 收藏操作 */
.favorite-actions {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-item {
  width: 56rpx;
  height: 56rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-item:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}

.favorite-actions .action-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.7;
}

/* 收藏时间 */
.favorite-time {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.time-text {
  font-size: 24rpx;
  color: #999999;
}

/* 加载状态 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666666;
}

.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #999999;
}

/* 底部安全区域 */
.safe-area {
  height: 60rpx;
  background-color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .nav-header {
    padding: 16rpx 24rpx;
  }
  
  .search-bar {
    padding: 20rpx 24rpx;
  }
  
  .stats-bar {
    padding: 16rpx 24rpx;
  }
  
  .tabs {
    padding: 0 24rpx;
  }
  
  .edit-toolbar {
    padding: 16rpx 24rpx;
  }
  
  .favorite-items {
    padding: 0 24rpx;
  }
  
  .favorite-item {
    padding: 28rpx;
  }
  
  .favorite-item.edit-mode {
    padding-left: 76rpx;
  }
  
  .food-favorite {
    gap: 20rpx;
  }
  
  .food-image {
    width: 140rpx;
    height: 140rpx;
  }
  
  .user-avatar {
    width: 100rpx;
    height: 100rpx;
  }
}

/* 深色模式适配（预留） */
@media (prefers-color-scheme: dark) {
  .favorites-container {
    background-color: #000000;
    color: #ffffff;
  }
  
  .nav-header,
  .search-bar,
  .tabs-container,
  .favorites-list {
    background-color: #000000;
    border-color: #333333;
  }
  
  .nav-title {
    color: #ffffff;
  }
  
  .stats-bar {
    background-color: #1a1a1a;
  }
  
  .action-btn {
    background-color: #ffffff;
  }
  
  .action-text {
    color: #000000;
  }
  
  .tab-item.active {
    background-color: #ffffff;
  }
  
  .tab-item.active .tab-text {
    color: #000000;
  }
  
  .favorite-item {
    background-color: #000000;
    border-color: #333333;
  }
  
  .favorite-item.selected {
    background-color: #1a1a1a;
    border-color: #ffffff;
  }
  
  .food-title,
  .post-title,
  .author-name,
  .name-text {
    color: #ffffff;
  }
  
  .checkbox {
    background-color: #000000;
    border-color: #666666;
  }
  
  .checkbox.checked {
    background-color: #ffffff;
    border-color: #ffffff;
  }
  
  .safe-area {
    background-color: #000000;
  }
}