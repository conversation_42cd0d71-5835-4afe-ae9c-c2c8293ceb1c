// pages/profile/profile.js
// 黑白极简个人页面 - Black & White Minimal Profile Page

const app = getApp();
// 引入认证工具函数
const { syncLocalDataToServer, getLocalData } = require('../../utils/auth.js');

Page({
  data: {
    isLoggedIn: false,
    userInfo: {
      name: '张三',
      id: '88888888',
      avatar: '/images/default-avatar.png',
      likedCount: 128,
      likeCount: 23,
      reviewCount: 45
    },
    cacheSize: '128MB'
  },

  onShow() {
    // 每次显示页面时同步登录状态
    this.syncLoginState();

    // 检查云开发环境状态（仅在调试模式下）
    this.checkCloudEnvironment();
  },

  /**
   * 检查云开发环境状态
   * 用于调试和验证云开发配置
   */
  checkCloudEnvironment() {
    const envConfig = app.getEnvironmentConfig();

    if (envConfig.debug && envConfig.useCloudFunction) {
      console.log('[Profile] 云开发环境检查:', {
        cloudEnvId: envConfig.cloudEnvId,
        cloudInitialized: app.globalData.cloudInitialized,
        useCloudFunction: envConfig.useCloudFunction
      });

      // 验证云开发连接（可选）
      if (app.globalData.cloudInitialized && typeof wx.cloud !== 'undefined') {
        wx.cloud.callFunction({
          name: 'auth',
          data: { action: 'ping' },
          timeout: 5000
        }).then(res => {
          console.log('[Profile] 云开发连接验证成功:', res.result);
        }).catch(err => {
          console.warn('[Profile] 云开发连接验证失败:', err);
        });
      }
    }
  },

  /**
   * 从全局数据同步登录状态到页面数据
   * 优化云开发用户数据处理
   */
  syncLoginState() {
    const globalUserInfo = app.globalData.userInfo;
    const isLoggedIn = app.globalData.isLoggedIn;

    console.log('[Profile] 同步登录状态:', {
      isLoggedIn: isLoggedIn,
      hasUserInfo: !!globalUserInfo,
      userInfo: globalUserInfo
    });

    // 处理用户信息数据格式
    let userInfo = this.data.userInfo; // 默认用户信息

    if (globalUserInfo) {
      // 适配云开发和传统后端的用户数据格式
      userInfo = {
        name: globalUserInfo.nickname || globalUserInfo.name || '微信用户',
        id: globalUserInfo.id || globalUserInfo.openid || '未知',
        avatar: globalUserInfo.avatar_url || globalUserInfo.avatarUrl || '/images/default-avatar.png',
        likedCount: globalUserInfo.likedCount || this.data.userInfo.likedCount,
        likeCount: globalUserInfo.likeCount || this.data.userInfo.likeCount,
        reviewCount: globalUserInfo.reviewCount || this.data.userInfo.reviewCount,
        // 云开发特有字段
        openid: globalUserInfo.openid,
        gender: globalUserInfo.gender,
        city: globalUserInfo.city,
        province: globalUserInfo.province,
        preferences: globalUserInfo.preferences
      };
    }

    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo
    });

    // 如果是云开发登录，记录额外信息
    const envConfig = app.getEnvironmentConfig();
    if (envConfig.useCloudFunction && isLoggedIn && globalUserInfo) {
      console.log('[Profile] 云开发用户数据同步完成:', {
        userId: globalUserInfo.id,
        nickname: globalUserInfo.nickname,
        cloudEnvId: envConfig.cloudEnvId
      });
    }
  },

  /**
   * 处理微信登录按钮点击
   * 直接触发微信授权弹窗，确保用户可以看到授权界面
   */
  handleLogin() {
    console.log('[Profile] 开始微信登录流程');

    // 检查网络状态
    wx.getNetworkType({
      success: (networkRes) => {
        if (networkRes.networkType === 'none') {
          wx.showModal({
            title: '网络异常',
            content: '请检查网络连接后重试',
            showCancel: false,
            confirmText: '知道了'
          });
          return;
        }
        
        // 网络正常，直接触发微信授权
        this.triggerWechatAuth();
      },
      fail: () => {
        // 网络检查失败，继续尝试登录
        console.warn('[Profile] 网络状态检查失败，继续尝试登录');
        this.triggerWechatAuth();
      }
    });
  },

  /**
   * 触发微信授权弹窗
   * 必须在用户点击事件中直接调用wx.getUserProfile才能弹出授权界面
   */
  triggerWechatAuth() {
    console.log('[Profile] 触发微信授权弹窗');
    
    // 直接调用微信授权API
    wx.getUserProfile({
      desc: '用于完善用户资料，提供更好的服务体验',
      success: (userRes) => {
        console.log('[Profile] 用户授权成功，开始登录流程');
        // 用户授权成功，继续登录流程
        this.performLoginWithUserInfo(userRes.userInfo);
      },
      fail: (error) => {
        console.log('[Profile] 用户取消授权或授权失败:', error);
        
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          // 用户主动取消授权
          wx.showModal({
            title: '授权提示',
            content: '需要您的授权才能使用完整功能，是否重新授权？',
            showCancel: true,
            confirmText: '重新授权',
            cancelText: '暂不授权',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 用户选择重新授权
                this.triggerWechatAuth();
              } else {
                // 用户选择暂不授权，使用匿名登录
                this.performAnonymousLogin();
              }
            }
          });
        } else {
          // 其他错误，提供重试选项
          wx.showModal({
            title: '授权失败',
            content: '获取授权失败，是否重试？',
            showCancel: true,
            confirmText: '重试',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                this.triggerWechatAuth();
              }
            }
          });
        }
      }
    });
  },

  /**
   * 执行带用户信息的登录流程
   * @param {Object} userInfo 微信用户信息
   */
  performLoginWithUserInfo(userInfo) {
    console.log('[Profile] 开始执行带用户信息的登录流程');
    
    // 检查云开发环境配置
    const envConfig = app.getEnvironmentConfig();
    console.log('[Profile] 当前环境配置:', envConfig);

    // 显示加载提示
    wx.showLoading({
      title: envConfig.useCloudFunction ? '正在登录...' : '模拟登录中...',
      mask: true
    });

    if (envConfig.useCloudFunction) {
      // 使用云开发登录
      this.executeCloudLoginWithUserInfo(userInfo);
    } else {
      // 使用模拟登录
      this.executeMockLoginWithUserInfo(userInfo);
    }
  },

  /**
   * 执行匿名登录流程
   */
  performAnonymousLogin() {
    console.log('[Profile] 开始执行匿名登录流程');
    
    // 检查云开发环境配置
    const envConfig = app.getEnvironmentConfig();
    
    // 显示加载提示
    wx.showLoading({
      title: '正在登录...',
      mask: true
    });

    if (envConfig.useCloudFunction) {
      // 使用云开发匿名登录
      this.executeCloudLoginWithUserInfo(null);
    } else {
      // 使用模拟匿名登录
      this.executeMockLoginWithUserInfo(null);
    }
  },

  /**
   * 执行云开发登录（带用户信息）
   * @param {Object} userInfo 微信用户信息，null表示匿名登录
   */
  executeCloudLoginWithUserInfo(userInfo) {
    console.log('[Profile] 执行云开发登录，用户信息:', userInfo ? '已授权' : '匿名');
    
    // 记录登录开始时间
    const loginStartTime = Date.now();

    // 先获取微信登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('[Profile] 获取微信登录凭证成功');
          // 直接调用云函数登录
          this.callCloudLoginFunction(loginRes.code, userInfo, loginStartTime);
        } else {
          wx.hideLoading();
          console.error('[Profile] 获取微信登录凭证失败:', loginRes.errMsg);
          this.handleLoginError({ message: '获取微信登录凭证失败' });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('[Profile] wx.login调用失败:', error);
        this.handleLoginError(error);
      }
    });
  },

  /**
   * 执行模拟登录（带用户信息）
   * @param {Object} userInfo 微信用户信息，null表示匿名登录
   */
  executeMockLoginWithUserInfo(userInfo) {
    console.log('[Profile] 执行模拟登录，用户信息:', userInfo ? '已授权' : '匿名');
    
    // 记录登录开始时间
    const loginStartTime = Date.now();
    
    // 模拟登录延迟
    setTimeout(() => {
      wx.hideLoading();
      
      const loginDuration = Date.now() - loginStartTime;
      console.log(`[Profile] 模拟登录耗时: ${loginDuration}ms`);
      
      // 构造模拟登录结果
      const mockResult = {
        userInfo: userInfo ? {
          id: 'mock_user_' + Date.now(),
          nickname: userInfo.nickName || '微信用户',
          avatar: userInfo.avatarUrl || '/images/default-avatar.png',
          gender: userInfo.gender || 0,
          city: userInfo.city || '',
          province: userInfo.province || '',
          country: userInfo.country || ''
        } : {
          id: 'anonymous_user_' + Date.now(),
          nickname: '匿名用户',
          avatar: '/images/default-avatar.png',
          gender: 0,
          city: '',
          province: '',
          country: ''
        },
        token: 'mock_token_' + Date.now(),
        isNewUser: true
      };
      
      console.log('[Profile] 模拟登录成功:', mockResult);
      this.handleLoginSuccess(mockResult, { useCloudFunction: false });
    }, 1000);
  },

  /**
   * 调用云函数进行登录
   * @param {string} code 微信登录凭证
   * @param {Object} userInfo 微信用户信息，null表示匿名登录
   * @param {number} loginStartTime 登录开始时间
   */
  callCloudLoginFunction(code, userInfo, loginStartTime) {
    console.log('[Profile] 调用云函数进行登录');
    
    // 调用auth云函数
    wx.cloud.callFunction({
      name: 'auth',
      data: {
        action: 'login',
        data: {
          code: code,
          userInfo: userInfo
        }
      },
      success: (res) => {
        wx.hideLoading();
        
        const loginDuration = Date.now() - loginStartTime;
        console.log(`[Profile] 云开发登录耗时: ${loginDuration}ms`);
        
        if (res.result && res.result.success) {
          console.log('[Profile] 云开发登录成功:', res.result.data);
          this.handleLoginSuccess(res.result.data, { useCloudFunction: true });
        } else {
          console.error('[Profile] 云开发登录失败:', res.result);
          this.handleLoginError({ message: res.result?.message || '云开发登录失败' });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        
        const loginDuration = Date.now() - loginStartTime;
        console.log(`[Profile] 云开发登录失败，耗时: ${loginDuration}ms`);
        
        console.error('[Profile] 云函数调用失败:', error);
        
        // 根据错误类型提供不同的错误处理
        if (error.errMsg && error.errMsg.includes('function not found')) {
          this.handleLoginError({ message: '登录服务暂时不可用，请稍后重试' });
        } else if (error.errMsg && error.errMsg.includes('timeout')) {
          this.handleLoginError({ message: '登录请求超时，请检查网络连接' });
        } else {
          this.handleLoginError({ message: error.errMsg || '登录失败，请重试' });
        }
      }
    });
  },

  /**
   * 处理登录错误
   */
  handleLoginError(err) {
    const errorMsg = err.message || err.errMsg || '登录失败';
    
    // 根据错误类型提供不同的处理方案
    if (errorMsg.includes('access_token expired') || errorMsg.includes('42001')) {
      // access_token过期错误
      wx.showModal({
        title: '登录状态过期',
        content: '检测到登录状态已过期，这通常是由于微信开发者工具登录过期导致的。\n\n建议解决方案：\n1. 重新登录微信开发者工具\n2. 重新编译小程序\n3. 清除缓存后重试',
        showCancel: true,
        cancelText: '稍后再试',
        confirmText: '立即重试',
        success: (modalRes) => {
          if (modalRes.confirm) {
            setTimeout(() => {
              this.handleLogin();
            }, 1000);
          }
        }
      });
    } else if (errorMsg.includes('timeout') || errorMsg.includes('网络')) {
      // 网络超时错误
      wx.showModal({
        title: '网络超时',
        content: '登录请求超时，请检查网络连接后重试。',
        showCancel: true,
        cancelText: '取消',
        confirmText: '重试',
        success: (modalRes) => {
          if (modalRes.confirm) {
            setTimeout(() => {
              this.handleLogin();
            }, 1000);
          }
        }
      });
    } else if (errorMsg.includes('function not found')) {
      // 云函数未部署错误
      wx.showModal({
        title: '服务异常',
        content: '登录服务暂时不可用，请稍后重试或联系客服。',
        showCancel: true,
        cancelText: '取消',
        confirmText: '重试',
        success: (modalRes) => {
          if (modalRes.confirm) {
            setTimeout(() => {
              this.handleLogin();
            }, 2000);
          }
        }
      });
    } else {
      // 其他错误
      wx.showModal({
        title: '登录失败',
        content: `登录过程中发生错误：${errorMsg}\n\n是否重试？`,
        showCancel: true,
        cancelText: '取消',
        confirmText: '重试',
        success: (modalRes) => {
          if (modalRes.confirm) {
            setTimeout(() => {
              this.handleLogin();
            }, 1000);
          }
        }
      });
    }
  },

  /**
   * 处理登录成功
   */
  handleLoginSuccess(res, envConfig) {
    // 同步登录状态
    this.syncLoginState();

    // 显示成功提示
    const isNewUser = res && res.isNewUser;
    const successMsg = isNewUser ? '注册成功，欢迎使用！' : '登录成功！';
    
    wx.showToast({
      title: successMsg,
      icon: 'success',
      duration: 2000
    });

    // 同步本地数据到服务器
    this.syncLocalDataAfterLogin();

    // 记录登录成功日志
    if (envConfig.useCloudFunction && res.userInfo) {
      console.log('[Profile] 云开发用户数据已存储:', {
        userId: res.userInfo.id,
        nickname: res.userInfo.nickname,
        isNewUser: isNewUser,
        cloudEnvId: envConfig.cloudEnvId
      });
      
      // 云开发登录成功的额外提示
      if (isNewUser) {
        setTimeout(() => {
          wx.showModal({
            title: '欢迎使用',
            content: '您的账户已成功创建并同步到云端，可以开始使用所有功能了！',
            showCancel: false,
            confirmText: '开始使用'
          });
        }, 2500);
      }
    } else {
      console.log('[Profile] 模拟登录成功，数据仅保存在本地');
    }
  },

  /**
   * 登录后同步本地数据到服务器
   * Sync local data to server after login
   */
  syncLocalDataAfterLogin() {
    try {
      // 检查是否有本地数据需要同步
      const localLikes = getLocalData('userLikes');
      const localCollects = getLocalData('userCollects');
      const localShares = getLocalData('userShares');
      
      const totalLocalData = localLikes.length + localCollects.length + localShares.length;
      
      if (totalLocalData === 0) {
        console.log('没有本地数据需要同步');
        return;
      }
      
      console.log(`开始同步本地数据: 点赞${localLikes.length}条, 收藏${localCollects.length}条, 分享${localShares.length}条`);
      
      // 显示同步提示
      wx.showLoading({
        title: '正在同步数据...',
        mask: true
      });
      
      // 并行同步所有类型的数据
      const syncPromises = [];
      
      // 同步点赞数据
      if (localLikes.length > 0) {
        syncPromises.push(
          syncLocalDataToServer('userLikes', (likeData) => {
            return this.syncSingleLikeToServer(likeData);
          })
        );
      }
      
      // 同步收藏数据
      if (localCollects.length > 0) {
        syncPromises.push(
          syncLocalDataToServer('userCollects', (collectData) => {
            return this.syncSingleCollectToServer(collectData);
          })
        );
      }
      
      // 同步分享数据
      if (localShares.length > 0) {
        syncPromises.push(
          syncLocalDataToServer('userShares', (shareData) => {
            return this.syncSingleShareToServer(shareData);
          })
        );
      }
      
      // 等待所有同步完成
      Promise.all(syncPromises)
        .then(() => {
          wx.hideLoading();
          wx.showToast({
            title: '数据同步完成',
            icon: 'success',
            duration: 2000
          });
          console.log('所有本地数据同步完成');
        })
        .catch((error) => {
          wx.hideLoading();
          console.error('数据同步失败:', error);
          wx.showToast({
            title: '数据同步失败',
            icon: 'none',
            duration: 2000
          });
        });
        
    } catch (error) {
      console.error('同步本地数据时发生错误:', error);
      wx.hideLoading();
    }
  },

  /**
   * 同步单个点赞数据到服务器
   */
  syncSingleLikeToServer(likeData) {
    return new Promise((resolve, reject) => {
      // TODO: 实现点赞数据的服务器同步
      console.log('同步点赞数据到服务器:', likeData);
      setTimeout(() => resolve(), 100); // 临时解决方案
    });
  },

  /**
   * 同步单个收藏数据到服务器
   */
  syncSingleCollectToServer(collectData) {
    return new Promise((resolve, reject) => {
      // TODO: 实现收藏数据的服务器同步
      console.log('同步收藏数据到服务器:', collectData);
      setTimeout(() => resolve(), 100); // 临时解决方案
    });
  },

  /**
   * 同步单个分享数据到服务器
   */
  syncSingleShareToServer(shareData) {
    return new Promise((resolve, reject) => {
      // TODO: 实现分享数据的服务器同步
      console.log('同步分享数据到服务器:', shareData);
      setTimeout(() => resolve(), 100); // 临时解决方案
    });
  },

  /**
   * 处理手机号登录按钮点击
   */
  handlePhoneLogin() {
    // 跳转到手机号登录页面
    wx.navigateTo({
      url: '/pages/phone-login/phone-login'
    });
  },

  /**
   * 处理统计数据点击
   */
  onStatTap(e) {
    const type = e.currentTarget.dataset.type;
    wx.showToast({
      title: `查看${type}`,
      icon: 'none'
    });
  },

  /**
   * 处理获赞记录点击
   */
  onLikedTap() {
    wx.showModal({
      title: '我的获赞',
      content: '获赞记录功能正在开发中！\n\n这里将显示您发布的内容获得的所有点赞记录。',
      showCancel: true,
      cancelText: '知道了',
      confirmText: '去社区',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/community/community'
          });
        }
      }
    });
  },

  /**
   * 处理点赞记录点击
   */
  onLikeTap() {
    wx.showModal({
      title: '我的点赞',
      content: '点赞记录功能正在开发中！\n\n这里将显示您点赞过的美食内容和精彩分享。',
      showCancel: true,
      cancelText: '知道了',
      confirmText: '去社区',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/community/community'
          });
        }
      }
    });
  },

  /**
   * 处理评价记录点击
   */
  onReviewTap() {
    wx.showModal({
      title: '我的评价',
      content: '评价功能正在开发中！\n\n这里将显示您对美食的评价和点赞记录。',
      showCancel: true,
      cancelText: '知道了',
      confirmText: '去社区',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/community/community'
          });
        }
      }
    });
  },

  /**
   * 处理快捷操作卡片点击
   */
  onQuickActionTap(e) {
    const action = e.currentTarget.dataset.action;
    
    switch(action) {
      case 'orders':
        wx.showModal({
          title: '我的订单',
          content: '订单管理功能正在开发中！\n\n这里将显示您的所有订单记录，包括待付款、待收货、已完成等状态。',
          showCancel: true,
          cancelText: '知道了',
          confirmText: '去首页',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({ url: '/pages/index/index' });
            }
          }
        });
        break;
      case 'favorites':
        wx.showModal({
          title: '我的收藏',
          content: '收藏功能正在开发中！\n\n这里将显示您收藏的美食、餐厅和精彩内容。',
          showCancel: true,
          cancelText: '知道了',
          confirmText: '去社区',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({ url: '/pages/community/community' });
            }
          }
        });
        break;
      case 'coupons':
        wx.showModal({
          title: '优惠券',
          content: '优惠券功能正在开发中！\n\n这里将显示您的优惠券，包括可用、已使用和已过期的券。',
          showCancel: true,
          cancelText: '知道了',
          confirmText: '去首页',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({ url: '/pages/index/index' });
            }
          }
        });
        break;
      case 'wallet':
        wx.showModal({
          title: '我的钱包',
          content: '钱包功能正在开发中！\n\n这里将显示您的余额、积分和消费记录。',
          showCancel: true,
          cancelText: '知道了',
          confirmText: '去首页',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({ url: '/pages/index/index' });
            }
          }
        });
        break;
      default:
        wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },

  /**
   * 处理个人信息点击
   */
  onUserInfoTap() {
    wx.navigateTo({
      url: '/pages/user-info/user-info'
    });
  },

  /**
   * 处理我的动态点击
   */
  onPostsTap() {
    wx.navigateTo({
      url: '/pages/my-posts/my-posts'
    });
  },

  /**
   * 处理收藏夹点击
   */
  onFavoritesTap() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    });
  },

  /**
   * 处理浏览历史点击
   */
  onHistoryTap() {
    wx.navigateTo({
      url: '/pages/browse-history/browse-history'
    });
  },

  /**
   * 处理清除缓存点击
   */
  onClearCacheTap() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除应用缓存吗？\n\n这将释放存储空间，但可能影响应用加载速度。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定清除',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '清除中...' });
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({ title: '缓存已清除', icon: 'success' });
            this.setData({ cacheSize: '0MB' });
          }, 1500);
        }
      }
    });
  },

  /**
   * 处理帮助中心点击
   */
  onHelpTap() {
    wx.navigateTo({
      url: '/pages/help-center/help-center'
    });
  },

  /**
   * 处理意见反馈点击
   */
  onFeedbackTap() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  /**
   * 处理关于应用点击
   */
  onAboutTap() {
    wx.showModal({
      title: '关于应用',
      content: '今天吃什么 v1.0.0\n\n一款帮助您解决选择困难的美食决策工具。\n\n© 2024 今天吃什么团队',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 处理退出登录点击
   */
  /**
   * 处理登出按钮点击
   * 增强错误处理，确保登出流程的稳定性
   */
  onLogoutTap() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      success: (res) => {
        if (res.confirm) {
          // 显示登出中的加载提示
          wx.showLoading({ 
            title: '退出中...', 
            mask: true 
          });
          
          try {
            // 调用应用实例的退出登录方法
            app.clearLoginStatus();
            
            // 同步页面状态
            this.syncLoginState();
            
            // 隐藏加载提示
            wx.hideLoading();
            
            // 显示退出成功提示并在完成后跳转
            wx.showToast({ 
              title: '已退出登录', 
              icon: 'success',
              duration: 1500,
              complete: () => {
                // Toast显示完成后立即跳转到首页
                wx.switchTab({
                  url: '/pages/index/index',
                  fail: (err) => {
                    console.error('跳转首页失败:', err);
                    // 如果跳转失败，尝试重新加载当前页面
                    wx.reLaunch({
                      url: '/pages/index/index'
                    });
                  }
                });
              }
            });
          } catch (error) {
            // 隐藏加载提示
            wx.hideLoading();
            
            console.error('登出过程中发生错误:', error);
            
            // 即使出错也要确保页面状态更新
            this.syncLoginState();
            
            // 显示警告信息但仍然跳转
            wx.showToast({
              title: '登出完成',
              icon: 'success',
              duration: 1500,
              complete: () => {
                wx.switchTab({
                  url: '/pages/index/index',
                  fail: () => {
                    wx.reLaunch({
                      url: '/pages/index/index'
                    });
                  }
                });
              }
            });
          }
        }
      },
      fail: (error) => {
        console.error('显示登出确认弹窗失败:', error);
      }
    });
  },


});