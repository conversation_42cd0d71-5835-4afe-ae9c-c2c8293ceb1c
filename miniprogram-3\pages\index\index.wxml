<!-- pages/index/index.wxml -->
<!-- 首页 - 3x3翻牌决策 -->

<view class="container">
  <!-- 顶部导航栏 (复用社区页样式) -->
  <view class="nav-header">
    <view class="nav-left">
      <text class="nav-title">今天吃什么？</text>
    </view>
    <view class="nav-right">
      <!-- 未来可以放设置或其他按钮 -->
    </view>
  </view>

  <!-- 主内容区域 -->
  <view class="content-scroll">
    <view class="main-content">
      <view class="flip-grid-container">
        <view class="page-subtitle">点击卡片，揭晓你的天选午餐</view>
        
        <!-- 3x3 翻牌网格 -->
        <view class="flip-grid">
          <view class="flip-card {{item.isSelected ? 'is-selected' : ''}}" wx:for="{{cards}}" wx:key="id">
            <view class="flip-card-inner {{item.isFlipped ? 'is-flipped' : ''}}" data-index="{{index}}" bindtap="flipCard">
              <view class="flip-card-front">?</view>
              <view class="flip-card-back">{{item.name}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 (根据是否已选择食物显示不同按钮) -->
      <view class="action-buttons">
        <block wx:if="{{!canConfirm}}">
          <view class="button" bindtap="goToPreferences">调整我的口味偏好</view>
          <view class="button primary" bindtap="resetGame">开始翻牌</view>
        </block>
        <block wx:else>
          <view class="button primary" bindtap="confirmSelection">就决定是你了！</view>
          <view class="button" bindtap="resetGame">不满意，再来一次</view>
        </block>
      </view>
    </view>
  </view>
</view>
