// pages/phone-login/phone-login.js
// 手机号登录页面

const app = getApp();

Page({
  data: {
    phone: '',
    code: '',
    countdown: 0,
    canSendCode: true,
    isLoading: false,
    codeButtonText: '获取验证码'
  },

  onLoad(options) {
    console.log('手机号登录页面加载');
  },

  /**
   * 手机号输入处理
   */
  onPhoneInput(e) {
    const phone = e.detail.value;
    this.setData({ phone });
    
    // 验证手机号格式
    this.validatePhone(phone);
  },

  /**
   * 验证码输入处理
   */
  onCodeInput(e) {
    const code = e.detail.value;
    this.setData({ code });
  },

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 发送验证码
   */
  async sendCode() {
    const { phone, canSendCode } = this.data;

    // 检查是否可以发送
    if (!canSendCode) {
      return;
    }

    // 验证手机号
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.validatePhone(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '发送中...' });

      // 调用后端API发送验证码
      const result = await this.requestSendCode(phone);

      wx.hideLoading();

      // 开始倒计时
      this.startCountdown();

      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      });

      // 开发环境显示验证码
      if (result.code) {
        console.log('开发环境验证码:', result.code);
        wx.showModal({
          title: '开发环境提示',
          content: `验证码：${result.code}\n\n生产环境将通过短信发送`,
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      });
    }
  },

  /**
   * 请求发送验证码
   */
  requestSendCode(phone) {
    return new Promise((resolve, reject) => {
      const envConfig = app.getEnvironmentConfig();
      
      wx.request({
        url: `${envConfig.apiBaseUrl}/auth/phone/send-code`,
        method: 'POST',
        data: {
          phone,
          type: 'login'
        },
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.message || '发送验证码失败'));
          }
        },
        fail: (error) => {
          console.error('发送验证码请求失败:', error);
          reject(new Error('网络请求失败'));
        }
      });
    });
  },

  /**
   * 开始倒计时
   */
  startCountdown() {
    let countdown = 60;
    this.setData({
      canSendCode: false,
      countdown,
      codeButtonText: `${countdown}s后重发`
    });

    const timer = setInterval(() => {
      countdown--;
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          canSendCode: true,
          countdown: 0,
          codeButtonText: '获取验证码'
        });
      } else {
        this.setData({
          countdown,
          codeButtonText: `${countdown}s后重发`
        });
      }
    }, 1000);
  },

  /**
   * 手机号登录
   */
  async handleLogin() {
    const { phone, code, isLoading } = this.data;

    if (isLoading) {
      return;
    }

    // 验证输入
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.validatePhone(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!code) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }

    if (!/^\d{6}$/.test(code)) {
      wx.showToast({
        title: '请输入6位数字验证码',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ isLoading: true });
      wx.showLoading({ title: '登录中...' });

      // 调用后端API登录
      const result = await this.requestLogin(phone, code);

      // 保存登录状态
      app.setLoginStatus(result.token, result.user);

      wx.hideLoading();

      wx.showToast({
        title: result.isNewUser ? '注册成功' : '登录成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      this.setData({ isLoading: false });
      wx.hideLoading();
      
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
    }
  },

  /**
   * 请求登录
   */
  requestLogin(phone, code) {
    return new Promise((resolve, reject) => {
      const envConfig = app.getEnvironmentConfig();
      
      wx.request({
        url: `${envConfig.apiBaseUrl}/auth/phone/login`,
        method: 'POST',
        data: {
          phone,
          code
        },
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.message || '登录失败'));
          }
        },
        fail: (error) => {
          console.error('登录请求失败:', error);
          reject(new Error('网络请求失败'));
        }
      });
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 微信登录
   * 直接触发微信授权弹窗
   */
  handleWechatLogin() {
    console.log('[PhoneLogin] 开始微信登录流程');
    
    // 直接调用微信授权API
    wx.getUserProfile({
      desc: '用于完善用户资料，提供更好的服务体验',
      success: (userRes) => {
        console.log('[PhoneLogin] 用户授权成功，开始登录流程');
        // 用户授权成功，继续登录流程
        this.performWechatLogin(userRes.userInfo);
      },
      fail: (error) => {
        console.log('[PhoneLogin] 用户取消授权或授权失败:', error);
        
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          // 用户主动取消授权
          wx.showModal({
            title: '授权提示',
            content: '需要您的授权才能使用微信登录功能',
            showCancel: false,
            confirmText: '知道了'
          });
        } else {
          // 其他错误，提供重试选项
          wx.showModal({
            title: '授权失败',
            content: '获取授权失败，是否重试？',
            showCancel: true,
            confirmText: '重试',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                this.handleWechatLogin();
              }
            }
          });
        }
      }
    });
  },

  /**
   * 执行微信登录流程
   * @param {Object} userInfo 微信用户信息
   */
  performWechatLogin(userInfo) {
    console.log('[PhoneLogin] 执行微信登录流程');
    
    // 显示加载提示
    wx.showLoading({
      title: '正在登录...',
      mask: true
    });
    
    // 获取微信登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('[PhoneLogin] 获取微信登录凭证成功');
          // 调用云函数进行登录
          this.callWechatLoginFunction(loginRes.code, userInfo);
        } else {
          wx.hideLoading();
          console.error('[PhoneLogin] 获取微信登录凭证失败:', loginRes.errMsg);
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('[PhoneLogin] wx.login调用失败:', error);
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 调用云函数进行微信登录
   * @param {string} code 微信登录凭证
   * @param {Object} userInfo 微信用户信息
   */
  callWechatLoginFunction(code, userInfo) {
    console.log('[PhoneLogin] 调用云函数进行微信登录');
    
    // 获取环境配置
    const app = getApp();
    const envConfig = app.getEnvironmentConfig();
    
    if (envConfig.useCloudFunction) {
      // 使用云开发登录
      wx.cloud.callFunction({
        name: 'auth',
        data: {
          action: 'login',
          data: {
            code: code,
            userInfo: userInfo
          }
        },
        success: (res) => {
          wx.hideLoading();
          
          if (res.result && res.result.success) {
            console.log('[PhoneLogin] 微信登录成功:', res.result.data);
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            // 登录成功后返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          } else {
            console.error('[PhoneLogin] 微信登录失败:', res.result);
            wx.showToast({
              title: '登录失败',
              icon: 'none'
            });
          }
        },
        fail: (error) => {
          wx.hideLoading();
          console.error('[PhoneLogin] 云函数调用失败:', error);
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 使用模拟登录
      setTimeout(() => {
        wx.hideLoading();
        console.log('[PhoneLogin] 模拟微信登录成功');
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        // 登录成功后返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
});
