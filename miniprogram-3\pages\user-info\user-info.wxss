/* pages/user-info/user-info.wxss */
/* 个人信息编辑页面样式 - 黑白极简设计 */

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: #F0F2F5;
  color: #333333;
  line-height: 1.6;
}

.container {
  max-width: 750rpx;
  margin: 0 auto;
  background-color: #F0F2F5;
  min-height: 100vh;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E0E0E0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-back {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.nav-back-icon {
  font-size: 36rpx;
  color: #333333;
  font-weight: 300;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  text-align: center;
}

.nav-action {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.nav-save {
  font-size: 28rpx;
  color: #000000; /* 改为黑色文字 */
  font-weight: 500;
}

/* 头像区域 */
.avatar-section {
  background: #ffffff;
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid #E0E0E0;
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 16rpx;
  cursor: pointer;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 2rpx solid #E0E0E0;
  background: #F8F9FA;
}

.avatar-edit-overlay {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.avatar-edit-text {
  font-size: 20rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: #666666;
}

/* 信息区域 */
.info-section {
  background: #ffffff;
  margin-bottom: 32rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 16rpx;
  overflow: hidden;
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.section-title {
  padding: 24rpx 32rpx 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #F0F2F5;
  background-color: #FAFAFA;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F2F5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.info-item:hover {
  background-color: #F8F9FA;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: #000000; /* 改为黑色背景 */
  transition: width 0.2s ease;
}

.info-item:hover::before {
  width: 4rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
  text-align: right;
  margin-right: 16rpx;
  word-break: break-all;
}

.info-value.signature {
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-arrow {
  color: #CCCCCC;
  font-size: 24rpx;
  font-weight: 300;
}

/* 选择器样式 */
picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* 底部安全区域 */
.safe-area {
  height: 68rpx;
  background: #F0F2F5;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .info-label {
    width: 120rpx;
    font-size: 26rpx;
  }
  
  .info-value {
    font-size: 26rpx;
  }
  
  .user-avatar {
    width: 140rpx;
    height: 140rpx;
  }
}

/* 动画效果 */
.info-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}