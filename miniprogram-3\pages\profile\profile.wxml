<!--pages/profile/profile.wxml-->
<!-- 个人页面 - 黑白极简设计 -->

<!-- 已登录状态 -->
<view class="container" wx:if="{{isLoggedIn}}">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"/>
    <view class="user-name">{{userInfo.nickName || 'ZHANG SAN'}}</view>
    <view class="user-id">ID: {{userInfo.userId || '88888888'}}</view>
    
    <view class="user-stats">
      <view class="stat-item" bindtap="onLikedTap">
        <text class="stat-number">{{userInfo.likedCount || '128'}}</text>
        <text class="stat-label">获赞</text>
      </view>
      <view class="stat-item" bindtap="onLikeTap">
        <text class="stat-number">{{userInfo.likeCount || '23'}}</text>
        <text class="stat-label">点赞</text>
      </view>
      <view class="stat-item" bindtap="onReviewTap">
        <text class="stat-number">{{userInfo.reviewCount || '45'}}</text>
        <text class="stat-label">评价</text>
      </view>
    </view>
  </view>

  <!-- 主要功能区域 -->
  <view class="main-content">
    

    <!-- 账户管理 -->
    <view class="section">
      <view class="section-title">账户管理</view>
      <view class="menu-item" bindtap="onUserInfoTap">
        <view class="menu-icon">○</view>
        <view class="menu-text">
          个人信息
          <view class="menu-subtitle">编辑个人资料和偏好设置</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
    </view>
    <!-- 内容管理 -->
    <view class="section">
      <view class="section-title">内容管理</view>
      <view class="menu-item" bindtap="onPostsTap">
        <view class="menu-icon">□</view>
        <view class="menu-text">
          我的动态
          <view class="menu-subtitle">查看和管理发布内容</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
      <view class="menu-item" bindtap="onFavoritesTap">
        <view class="menu-icon">◎</view>
        <view class="menu-text">
          收藏夹
          <view class="menu-subtitle">保存的内容和书签</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
      <view class="menu-item" bindtap="onHistoryTap">
        <view class="menu-icon">⬢</view>
        <view class="menu-text">
          浏览历史
          <view class="menu-subtitle">最近查看的内容记录</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
    </view>

    

    <!-- 帮助支持 -->
    <view class="section">
      <view class="section-title">帮助支持</view>
      <view class="menu-item" bindtap="onHelpTap">
        <view class="menu-icon">?</view>
        <view class="menu-text">
          帮助中心
          <view class="menu-subtitle">常见问题和使用指南</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
      <view class="menu-item" bindtap="onFeedbackTap">
        <view class="menu-icon">!</view>
        <view class="menu-text">
          意见反馈
          <view class="menu-subtitle">报告问题和建议</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
      <view class="menu-item" bindtap="onAboutTap">
        <view class="menu-icon">i</view>
        <view class="menu-text">
          关于应用
          <view class="menu-subtitle">版本信息和法律条款</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
    </view>
  </view>



  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="onLogoutTap">退出登录</button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>

<!-- 未登录状态 -->
<view class="login-container" wx:else>
  <!-- 登录引导头部 -->
  <view class="login-header">
    <image class="login-avatar" src="/images/default-avatar.png" mode="aspectFill"/>
    <view class="login-title">欢迎使用今天吃什么</view>
    <view class="login-subtitle">登录后享受更多个性化服务</view>
  </view>

  <!-- 登录功能介绍 -->
  <view class="login-features">
    <view class="feature-item">
      <view class="feature-icon">◎</view>
      <view class="feature-text">
        <view class="feature-title">个性化推荐</view>
        <view class="feature-desc">根据您的喜好推荐美食</view>
      </view>
    </view>
    <view class="feature-item">
      <view class="feature-icon">□</view>
      <view class="feature-text">
        <view class="feature-title">发布动态</view>
        <view class="feature-desc">分享您的美食体验</view>
      </view>
    </view>
    <view class="feature-item">
      <view class="feature-icon">⬢</view>
      <view class="feature-text">
        <view class="feature-title">收藏历史</view>
        <view class="feature-desc">保存喜欢的内容</view>
      </view>
    </view>
  </view>

  <!-- 登录按钮 -->
  <view class="login-actions">
    <button class="login-btn wechat-btn" bindtap="handleLogin">微信登录</button>
    <button class="login-btn phone-btn" bindtap="handlePhoneLogin">手机号登录</button>
    <view class="login-tips">登录即表示同意《用户协议》和《隐私政策》</view>
  </view>

  <!-- 游客模式 -->
  <view class="guest-section">
    <view class="guest-title">暂不登录，继续浏览</view>
    <view class="guest-menu">
      <view class="guest-item" bindtap="onHelpTap">
        <view class="guest-icon">?</view>
        <view class="guest-text">帮助中心</view>
      </view>
      <view class="guest-item" bindtap="onAboutTap">
        <view class="guest-icon">i</view>
        <view class="guest-text">关于应用</view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>