// pages/help-center/help-center.js
// 帮助中心页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    searchKeyword: '', // 搜索关键词
    activeCategory: 'all', // 当前选中的分类
    categories: [
      { key: 'all', label: '全部', icon: '📋' },
      { key: 'basic', label: '基础使用', icon: '🔰' },
      { key: 'account', label: '账户相关', icon: '👤' },
      { key: 'food', label: '美食推荐', icon: '🍽️' },
      { key: 'technical', label: '技术问题', icon: '⚙️' },
      { key: 'other', label: '其他问题', icon: '❓' }
    ],
    helpItems: [], // 帮助条目列表
    filteredItems: [], // 过滤后的帮助条目
    expandedItems: [], // 展开的条目ID列表
    hotQuestions: [], // 热门问题
    contactInfo: {
      email: '<EMAIL>',
      phone: '************',
      workTime: '工作日 9:00-18:00'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initHelpData();
    this.loadHotQuestions();
  },

  /**
   * 初始化帮助数据
   */
  initHelpData() {
    const helpItems = [
      // 基础使用
      {
        id: 1,
        category: 'basic',
        question: '如何开始使用"今天吃什么"？',
        answer: '欢迎使用"今天吃什么"！首次使用时，您可以：\n1. 设置您的饮食偏好\n2. 点击"开始翻牌"进行美食推荐\n3. 根据推荐结果做出选择\n4. 在社区分享您的美食体验',
        tags: ['新手', '入门'],
        viewCount: 1250
      },
      {
        id: 2,
        category: 'basic',
        question: '翻牌功能是如何工作的？',
        answer: '翻牌功能是我们的核心推荐算法：\n1. 系统会根据您的偏好设置\n2. 结合当前时间、天气等因素\n3. 从美食数据库中智能筛选\n4. 以翻牌的形式呈现推荐结果\n5. 您可以重新翻牌直到满意为止',
        tags: ['翻牌', '推荐'],
        viewCount: 980
      },
      {
        id: 3,
        category: 'basic',
        question: '如何设置我的饮食偏好？',
        answer: '设置饮食偏好很简单：\n1. 进入"偏好设置"页面\n2. 选择您喜欢的菜系类型\n3. 设置口味偏好（辣度、甜度等）\n4. 添加过敏信息和忌口\n5. 保存设置后系统会据此推荐',
        tags: ['偏好', '设置'],
        viewCount: 756
      },
      // 账户相关
      {
        id: 4,
        category: 'account',
        question: '如何修改个人信息？',
        answer: '修改个人信息的步骤：\n1. 进入"个人页面"\n2. 点击"个人信息"\n3. 选择要修改的项目\n4. 输入新的信息\n5. 点击保存完成修改',
        tags: ['个人信息', '修改'],
        viewCount: 642
      },
      {
        id: 5,
        category: 'account',
        question: '忘记密码怎么办？',
        answer: '如果忘记密码，请：\n1. 在登录页面点击"忘记密码"\n2. 输入注册时的手机号或邮箱\n3. 获取验证码\n4. 设置新密码\n5. 使用新密码登录',
        tags: ['密码', '找回'],
        viewCount: 523
      },
      // 美食推荐
      {
        id: 6,
        category: 'food',
        question: '推荐的美食不合口味怎么办？',
        answer: '如果推荐不合口味，您可以：\n1. 点击"不满意，再来一次"重新推荐\n2. 在偏好设置中调整您的口味偏好\n3. 添加不喜欢的菜系到黑名单\n4. 系统会学习您的选择习惯，逐步优化推荐',
        tags: ['推荐', '优化'],
        viewCount: 834
      },
      {
        id: 7,
        category: 'food',
        question: '如何查看附近的餐厅？',
        answer: '查看附近餐厅的方法：\n1. 确保已开启位置权限\n2. 在推荐结果页面点击"查看附近"\n3. 系统会显示附近提供该美食的餐厅\n4. 可以查看餐厅评分、距离等信息\n5. 支持一键导航到餐厅',
        tags: ['餐厅', '位置'],
        viewCount: 712
      },
      // 技术问题
      {
        id: 8,
        category: 'technical',
        question: '应用加载很慢怎么办？',
        answer: '应用加载慢的解决方法：\n1. 检查网络连接是否正常\n2. 清除应用缓存\n3. 重启应用\n4. 更新到最新版本\n5. 如问题持续，请联系客服',
        tags: ['性能', '加载'],
        viewCount: 445
      },
      {
        id: 9,
        category: 'technical',
        question: '无法获取位置信息？',
        answer: '位置获取问题的解决方案：\n1. 检查是否授权位置权限\n2. 确保GPS功能已开启\n3. 在设置中重新授权应用位置权限\n4. 重启应用重新获取位置\n5. 可以手动选择城市作为替代方案',
        tags: ['位置', '权限'],
        viewCount: 367
      },
      // 其他问题
      {
        id: 10,
        category: 'other',
        question: '如何联系客服？',
        answer: '联系客服的方式：\n1. 应用内意见反馈功能\n2. 发送邮件至：<EMAIL>\n3. 拨打客服热线：************\n4. 工作时间：工作日 9:00-18:00\n5. 我们会在24小时内回复您的问题',
        tags: ['客服', '联系'],
        viewCount: 289
      }
    ];

    this.setData({
      helpItems,
      filteredItems: helpItems
    });
  },

  /**
   * 加载热门问题
   */
  loadHotQuestions() {
    // 根据浏览量排序，取前5个作为热门问题
    const hotQuestions = this.data.helpItems
      .sort((a, b) => b.viewCount - a.viewCount)
      .slice(0, 5)
      .map(item => ({
        id: item.id,
        question: item.question,
        viewCount: item.viewCount
      }));

    this.setData({ hotQuestions });
  },

  /**
   * 搜索输入处理
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    this.filterItems();
  },

  /**
   * 清除搜索
   */
  onClearSearch() {
    this.setData({ searchKeyword: '' });
    this.filterItems();
  },

  /**
   * 分类切换
   */
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ activeCategory: category });
    this.filterItems();
  },

  /**
   * 过滤帮助条目
   */
  filterItems() {
    let filteredItems = this.data.helpItems;

    // 按分类过滤
    if (this.data.activeCategory !== 'all') {
      filteredItems = filteredItems.filter(item => item.category === this.data.activeCategory);
    }

    // 按关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filteredItems = filteredItems.filter(item => 
        item.question.toLowerCase().includes(keyword) ||
        item.answer.toLowerCase().includes(keyword) ||
        item.tags.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    this.setData({ filteredItems });
  },

  /**
   * 展开/收起问题详情
   */
  onToggleItem(e) {
    const itemId = e.currentTarget.dataset.id;
    const expandedItems = [...this.data.expandedItems];
    const index = expandedItems.indexOf(itemId);

    if (index > -1) {
      // 收起
      expandedItems.splice(index, 1);
    } else {
      // 展开
      expandedItems.push(itemId);
      // 增加浏览量
      this.increaseViewCount(itemId);
    }

    this.setData({ expandedItems });
  },

  /**
   * 增加浏览量
   */
  increaseViewCount(itemId) {
    const helpItems = this.data.helpItems.map(item => {
      if (item.id === itemId) {
        return { ...item, viewCount: item.viewCount + 1 };
      }
      return item;
    });

    this.setData({ helpItems });
    this.filterItems();
  },

  /**
   * 点击热门问题
   */
  onHotQuestionTap(e) {
    const itemId = e.currentTarget.dataset.id;
    this.onToggleItem({ currentTarget: { dataset: { id: itemId } } });
    
    // 滚动到对应问题
    wx.pageScrollTo({
      selector: `#help-item-${itemId}`,
      duration: 300
    });
  },

  /**
   * 联系客服
   */
  onContactSupport(e) {
    const type = e.currentTarget.dataset.type;
    
    switch (type) {
      case 'email':
        wx.setClipboardData({
          data: this.data.contactInfo.email,
          success: () => {
            wx.showToast({
              title: '邮箱已复制',
              icon: 'success'
            });
          }
        });
        break;
      case 'phone':
        wx.makePhoneCall({
          phoneNumber: this.data.contactInfo.phone
        });
        break;
      case 'feedback':
        wx.navigateTo({
          url: '/pages/feedback/feedback'
        });
        break;
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 获取分类名称
   */
  getCategoryName(categoryKey) {
    const category = this.data.categories.find(cat => cat.key === categoryKey);
    return category ? category.label : '其他';
  }
});