<!--pages/feedback/feedback.wxml-->
<!-- 建议反馈页面 -->

<view class="feedback-container">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="onBack">
      <image class="back-icon" src="/images/svg/arrow-left.svg" mode="aspectFit"></image>
    </view>
    <view class="nav-title">建议反馈</view>
    <view class="nav-right">
      <view class="history-btn" bindtap="onShowHistory">
        <image class="history-icon" src="images/history.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 快捷入口 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="onShowFAQ">
      <image class="action-icon" src="images/help.png" mode="aspectFit"></image>
      <text class="action-text">常见问题</text>
    </view>
    <view class="action-item" bindtap="onContactService">
      <image class="action-icon" src="images/help.png" mode="aspectFit"></image>
      <text class="action-text">联系客服</text>
    </view>
  </view>

  <!-- 反馈类型选择 -->
  <view class="feedback-types">
    <view class="section-title">反馈类型</view>
    
    <view class="type-grid">
      <view 
        class="type-item {{formData.type === item.value ? 'selected' : ''}}"
        wx:for="{{feedbackTypes}}"
        wx:key="value"
        data-type="{{item.value}}"
        bindtap="onSelectType"
      >
        <view class="type-icon" style="background-color: {{item.color}}20; border-color: {{item.color}}">
          <image class="icon-image" src="images/help.png" mode="aspectFit" style="filter: {{formData.type === item.value ? 'brightness(0) invert(1)' : ''}}"></image>
        </view>
        <text class="type-name">{{item.name}}</text>
        <view class="type-check" wx:if="{{formData.type === item.value}}">
          <image class="check-icon" src="/images/svg/check.svg" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 反馈表单 -->
  <view class="feedback-form" wx:if="{{formData.type}}">
    <!-- 标题输入 -->
    <view class="form-section">
      <view class="section-title">
        <text>反馈标题</text>
        <text class="char-count">{{formData.title.length}}/{{titleMaxLength}}</text>
      </view>
      
      <view class="input-wrapper">
        <input 
          class="title-input"
          placeholder="请简要描述您的问题或建议"
          value="{{formData.title}}"
          maxlength="{{titleMaxLength}}"
          bindinput="onTitleInput"
        />
      </view>
    </view>

    <!-- 内容输入 -->
    <view class="form-section">
      <view class="section-title">
        <text>详细描述</text>
        <text class="char-count">{{formData.content.length}}/{{contentMaxLength}}</text>
      </view>
      
      <view class="textarea-wrapper">
        <textarea 
          class="content-textarea"
          placeholder="请详细描述您遇到的问题或建议，包括具体的操作步骤、期望结果等"
          value="{{formData.content}}"
          maxlength="{{contentMaxLength}}"
          bindinput="onContentInput"
          auto-height
          show-confirm-bar="{{false}}"
        ></textarea>
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">
        <text>上传图片</text>
        <text class="optional-text">(可选，最多{{maxImages}}张)</text>
      </view>
      
      <view class="image-upload">
        <view class="image-list">
          <view 
            class="image-item"
            wx:for="{{formData.images}}"
            wx:key="index"
            data-index="{{index}}"
            bindtap="onPreviewImage"
          >
            <image class="uploaded-image" src="{{item}}" mode="aspectFill"></image>
            <view class="image-delete" data-index="{{index}}" catchtap="onDeleteImage">
              <image class="delete-icon" src="images/close.png" mode="aspectFit"></image>
            </view>
          </view>
          
          <view 
            class="add-image-btn"
            wx:if="{{formData.images.length < maxImages}}"
            bindtap="onChooseImage"
          >
            <image class="add-icon" src="images/image.png" mode="aspectFit"></image>
            <text class="add-text">添加图片</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 优先级选择 -->
    <view class="form-section">
      <view class="section-title">优先级</view>
      
      <view class="priority-options">
        <view 
          class="priority-item {{formData.priority === item.value ? 'selected' : ''}}"
          wx:for="{{priorityOptions}}"
          wx:key="value"
          data-priority="{{item.value}}"
          bindtap="onSelectPriority"
        >
          <view class="priority-dot" style="background-color: {{item.color}}"></view>
          <text class="priority-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section" wx:if="{{!formData.anonymous}}">
      <view class="section-title">
        <text>联系方式</text>
        <text class="optional-text">(可选)</text>
      </view>
      
      <view class="input-wrapper">
        <input 
          class="contact-input"
          placeholder="请输入您的邮箱或手机号，方便我们联系您"
          value="{{formData.contact}}"
          bindinput="onContactInput"
        />
      </view>
    </view>

    <!-- 其他选项 -->
    <view class="form-section">
      <view class="section-title">其他选项</view>
      
      <view class="option-list">
        <view class="option-item">
          <view class="option-info">
            <text class="option-name">匿名反馈</text>
            <text class="option-desc">不记录您的个人信息</text>
          </view>
          <switch 
            class="option-switch" 
            checked="{{formData.anonymous}}"
            bindchange="onToggleAnonymous"
          />
        </view>
        
        <view class="option-item" wx:if="{{!formData.anonymous}}">
          <view class="option-info">
            <text class="option-name">允许联系</text>
            <text class="option-desc">允许我们就此反馈联系您</text>
          </view>
          <switch 
            class="option-switch" 
            checked="{{formData.allowContact}}"
            bindchange="onToggleContact"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section" wx:if="{{formData.type}}">
    <button 
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      disabled="{{submitting}}"
      bindtap="onSubmitFeedback"
    >
      <text class="submit-text">{{submitting ? '提交中...' : '提交反馈'}}</text>
      <view class="loading-icon" wx:if="{{submitting}}">
        <image class="loading-image" src="images/loading.gif" mode="aspectFit"></image>
      </view>
    </button>
    
    <view class="submit-tips">
      <text class="tips-text">我们会认真处理每一条反馈，感谢您的支持！</text>
    </view>
  </view>

  <!-- 历史反馈模态框 -->
  <view class="history-modal" wx:if="{{showHistory}}">
    <view class="modal-mask" bindtap="onHideHistory"></view>
    <view class="history-content">
      <view class="history-header">
        <text class="history-title">历史反馈</text>
        <view class="history-close" bindtap="onHideHistory">
          <image class="close-icon" src="images/close.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="history-list" wx:if="{{historyList.length > 0}}">
        <view 
          class="history-item"
          wx:for="{{historyList}}"
          wx:key="id"
          data-item="{{item}}"
          bindtap="onViewHistoryDetail"
        >
          <view class="history-info">
            <view class="history-type">{{getTypeName(item.type)}}</view>
            <view class="history-title-text">{{item.title}}</view>
            <view class="history-time">{{item.submitTime}}</view>
          </view>
          <view class="history-status">
            <text class="status-text status-{{item.status}}">{{getStatusText(item.status)}}</text>
          </view>
        </view>
      </view>
      
      <view class="empty-history" wx:else>
        <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
        <text class="empty-text">暂无历史反馈</text>
      </view>
    </view>
  </view>

  <!-- 常见问题模态框 -->
  <view class="faq-modal" wx:if="{{showFAQ}}">
    <view class="modal-mask" bindtap="onHideFAQ"></view>
    <view class="faq-content">
      <view class="faq-header">
        <text class="faq-title">常见问题</text>
        <view class="faq-close" bindtap="onHideFAQ">
          <image class="close-icon" src="images/close.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="faq-list">
        <view class="faq-item" wx:for="{{faqList}}" wx:key="index">
          <view class="faq-question">
            <image class="question-icon" src="images/help.png" mode="aspectFit"></image>
            <text class="question-text">{{item.question}}</text>
          </view>
          <view class="faq-answer">
            <text class="answer-text">{{item.answer}}</text>
          </view>
        </view>
      </view>
      
      <view class="faq-footer">
        <text class="footer-text">如果以上问题无法解决您的疑问，请提交反馈或联系客服。</text>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>