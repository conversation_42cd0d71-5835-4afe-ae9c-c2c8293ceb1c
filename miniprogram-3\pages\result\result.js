// pages/result/result.js
// 获取全局应用实例
const app = getApp();

// 模拟一个菜品数据库
const dishDatabase = {
    '麻辣小龙虾': {
        name: '麻辣小龙虾',
        subName: '夜市灵魂，麻辣鲜香',
        rating: 4.9,
        sales: 3200,
        price: 88,
        deliveryTime: 35,
        promo: '满100减15',
        platforms: [
            { name: '饿了么', price: 88, logo: '饿' },
            { name: '美团', price: 90, logo: '美' },
        ],
        recommendations: [
            { dishName: '蒜蓉小龙虾', userName: '爱吃虾的小王' },
            { dishName: '十三香小龙虾', userName: '隔壁老张' },
            { dishName: '油焖大虾', userName: '美食家李' },
        ]
    },
    '蒸汽海鲜': {
        name: '蒸汽海鲜',
        subName: '原汁原味，鲜甜可口',
        rating: 4.8,
        sales: 2380,
        price: 128,
        deliveryTime: 28,
        promo: '满200减30',
        platforms: [
            { name: '盒马', price: 128, logo: '盒' },
            { name: '京东到家', price: 130, logo: '京' },
            { name: '美团', price: 132, logo: '美' },
        ],
        recommendations: [
            { dishName: '波士顿龙虾', userName: '海鲜控' },
            { dishName: '扇贝粉丝', userName: '小红' },
            { dishName: '生蚝', userName: '爱吃蚝的 boy' },
        ]
    },
    // 你可以根据首页的 foodOptions 在这里添加更多菜品数据
    '烧烤': {
        name: '烧烤',
        subName: '夏日必备，烟火气息',
        rating: 4.7,
        sales: 5000,
        price: 50,
        deliveryTime: 40,
        promo: '满80减10',
        platforms: [
            { name: '饿了么', price: 50, logo: '饿' },
            { name: '美团', price: 52, logo: '美' },
        ],
        recommendations: [
            { dishName: '烤羊肉串', userName: '路边摊爱好者' },
            { dishName: '烤茄子', userName: '素食主义' },
            { dishName: '烤鸡翅', userName: '甜辣党' },
        ]
    }
};

Page({
    data: {
        dish: null // 初始化为空
    },

    /**
     * @description 页面加载时接收URL参数或从全局数据获取
     * @param {Object} options 页面参数
     */
    onLoad(options) {
        // 优先从全局数据获取当前选中的食物
        if (app.globalData.currentResult) {
            const selectedFood = app.globalData.currentResult;
            const dishData = dishDatabase[selectedFood.name] || this.createDefaultDish(selectedFood.name);
            // 从社区数据更新推荐信息
            this.updateRecommendationsFromCommunity(dishData);
            this.setData({
                dish: dishData
            });
            console.log('从全局数据加载食物：', selectedFood.name);
            return;
        }
        
        // 兼容URL参数方式（如果有的话）
        if (options.food) {
            const foodName = decodeURIComponent(options.food);
            const dishData = dishDatabase[foodName] || this.createDefaultDish(foodName);
            // 从社区数据更新推荐信息
            this.updateRecommendationsFromCommunity(dishData);
            this.setData({
                dish: dishData
            });
            
            // 同时更新全局数据，保持兼容性
            app.globalData.lastResult = foodName;
            console.log('从URL参数加载食物：', foodName);
        }
    },

    /**
     * @description 从社区数据更新推荐信息
     * @param {Object} dishData 菜品数据对象
     */
    updateRecommendationsFromCommunity(dishData) {
        // 获取社区发布的前三条数据
        const communityFeed = app.globalData.communityFeed || [];
        const topThreePosts = communityFeed.slice(0, 3);
        
        if (topThreePosts.length > 0) {
            // 将社区数据转换为推荐格式
            const recommendations = topThreePosts.map(post => ({
                dishName: post.location ? post.location.storeName : '美味推荐', // 使用店铺名称作为菜品名
                userName: post.user.userName, // 用户名
                imageUrl: post.imageUrl || '/images/default-dish.png' // 用户发布的图片
            }));
            
            // 更新菜品数据的推荐信息
            dishData.recommendations = recommendations;
            console.log('已从社区数据更新推荐信息：', recommendations.length, '条');
        } else {
            console.log('暂无社区数据，使用默认推荐信息');
        }
    },

    onShow() {
        // 每次页面显示时，都尝试获取最新的选择结果
        const lastResult = app.globalData.lastResult;
        if (lastResult && !this.data.dish) {
            // 如果有上次的选择结果，但当前页面没有显示菜品，则加载该菜品
            const dishData = dishDatabase[lastResult] || this.createDefaultDish(lastResult);
            // 从社区数据更新推荐信息
            this.updateRecommendationsFromCommunity(dishData);
            this.setData({
                dish: dishData
            });
        } else if (this.data.dish) {
            // 如果已有菜品数据，更新推荐信息以获取最新的社区内容
            const dishData = { ...this.data.dish };
            this.updateRecommendationsFromCommunity(dishData);
            this.setData({
                dish: dishData
            });
            console.log('页面显示时更新推荐信息');
        } else if (lastResult) {
            const dishData = dishDatabase[lastResult] || this.createDefaultDish(lastResult);
            // 从社区数据更新推荐信息
            this.updateRecommendationsFromCommunity(dishData);
            this.setData({
                dish: dishData
            });
        } else {
            this.setData({
                dish: null
            });
        }
    },

    // 如果数据库里没有，创建一个默认的菜品对象
    createDefaultDish(dishName) {
        return {
            name: dishName,
            subName: '新鲜出炉，美味可口',
            rating: 4.5, // 默认评分
            sales: 100, // 默认销量
            price: 30, // 默认价格
            deliveryTime: 30, // 默认配送时间
            promo: '暂无优惠',
            platforms: [
                { name: '饿了么', price: 30, logo: '饿' },
                { name: '美团', price: 31, logo: '美' },
            ],
            recommendations: [
                { dishName: '招牌推荐', userName: '店长' },
                { dishName: '热门菜品', userName: '大家' },
                { dishName: '新品尝鲜', userName: '小明' },
            ]
        }
    },

    // 重新抽选
    onReroll() {
        // 清除所有全局选择数据，确保首页完全重置
        app.globalData.lastResult = null;
        app.globalData.currentResult = null;
        console.log('清除全局数据，准备重新抽选');
        
        wx.switchTab({
            url: '/pages/index/index'
        });
    },

    // 点击去下单按钮
    onOrderTap(e) {
        const platform = e.currentTarget.dataset.platform;
        wx.showToast({
            title: `即将跳转到${platform}下单`,
            icon: 'none',
            duration: 1500
        });
        // 实际应用中，这里会进行跳转到外部App或小程序
    },

    // 点击大家都在吃推荐卡片
    onRecommendationTap(e) {
        const dishName = e.currentTarget.dataset.dishname;
        wx.showToast({
            title: `查看【${dishName}】详情`,
            icon: 'none',
            duration: 1500
        });
        // 实际应用中，这里会跳转到该菜品的详情页
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        if (this.data.dish) {
            return {
                title: `今天我吃了【${this.data.dish.name}】，你也来试试吧！`,
                path: `/pages/result/result?dish=${this.data.dish.name}` // 允许通过链接分享结果
            }
        }
        return {
            title: '今天吃什么？我帮你决定！',
            path: '/pages/index/index'
        }
    }
});