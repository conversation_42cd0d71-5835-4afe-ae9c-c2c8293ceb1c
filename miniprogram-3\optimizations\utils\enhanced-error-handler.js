/**
 * 增强的错误处理器
 * 提供统一的错误处理、日志记录和用户提示
 */
class EnhancedErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.maxQueueSize = 100;
    this.reportInterval = 5000; // 5秒上报一次
    this.isReporting = false;
    
    // 启动定时上报
    this.startPeriodicReport();
  }

  /**
   * 处理错误
   * @param {string} context 错误上下文
   * @param {Error|string} error 错误对象或错误信息
   * @param {Object} extra 额外信息
   */
  static handleError(context, error, extra = {}) {
    const instance = this.getInstance();
    instance.handle(context, error, extra);
  }

  /**
   * 获取单例实例
   * @returns {EnhancedErrorHandler} 错误处理器实例
   */
  static getInstance() {
    if (!this.instance) {
      this.instance = new EnhancedErrorHandler();
    }
    return this.instance;
  }

  /**
   * 处理错误的核心方法
   * @param {string} context 错误上下文
   * @param {Error|string} error 错误对象或错误信息
   * @param {Object} extra 额外信息
   */
  handle(context, error, extra = {}) {
    const errorInfo = this.formatError(context, error, extra);
    
    // 记录错误日志
    this.logError(errorInfo);
    
    // 添加到错误队列
    this.addToQueue(errorInfo);
    
    // 显示用户友好的错误提示
    this.showUserMessage(errorInfo);
    
    // 特殊错误处理
    this.handleSpecialErrors(errorInfo);
  }

  /**
   * 格式化错误信息
   * @param {string} context 错误上下文
   * @param {Error|string} error 错误对象或错误信息
   * @param {Object} extra 额外信息
   * @returns {Object} 格式化后的错误信息
   */
  formatError(context, error, extra) {
    const timestamp = new Date().toISOString();
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : null;
    
    return {
      id: this.generateErrorId(),
      timestamp,
      context,
      message: errorMessage,
      stack,
      extra,
      userAgent: this.getUserAgent(),
      appVersion: this.getAppVersion(),
      page: this.getCurrentPage()
    };
  }

  /**
   * 记录错误日志
   * @param {Object} errorInfo 错误信息
   */
  logError(errorInfo) {
    console.group('🚨 错误 [' + errorInfo.context + ']');
    console.error('消息:', errorInfo.message);
    console.error('时间:', errorInfo.timestamp);
    console.error('页面:', errorInfo.page);
    if (errorInfo.stack) {
      console.error('堆栈:', errorInfo.stack);
    }
    if (Object.keys(errorInfo.extra).length > 0) {
      console.error('额外信息:', errorInfo.extra);
    }
    console.groupEnd();
  }

  /**
   * 添加错误到队列
   * @param {Object} errorInfo 错误信息
   */
  addToQueue(errorInfo) {
    this.errorQueue.push(errorInfo);
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * 显示用户友好的错误提示
   * @param {Object} errorInfo 错误信息
   */
  showUserMessage(errorInfo) {
    const userMessage = this.getUserFriendlyMessage(errorInfo);
    
    wx.showToast({
      title: userMessage,
      icon: 'none',
      duration: 3000
    });
  }

  /**
   * 获取用户友好的错误消息
   * @param {Object} errorInfo 错误信息
   * @returns {string} 用户友好的错误消息
   */
  getUserFriendlyMessage(errorInfo) {
    const { context, message } = errorInfo;
    
    // 根据错误上下文返回不同的用户提示
    const messageMap = {
      '网络请求': '网络连接异常，请检查网络后重试',
      '数据加载': '数据加载失败，请稍后重试',
      '用户登录': '登录失败，请重新登录',
      '文件上传': '文件上传失败，请重试',
      '支付处理': '支付处理异常，请联系客服',
      '页面跳转': '页面跳转失败，请重试'
    };
    
    return messageMap[context] || '操作失败，请重试';
  }

  /**
   * 处理特殊错误
   * @param {Object} errorInfo 错误信息
   */
  handleSpecialErrors(errorInfo) {
    const { context, message } = errorInfo;
    
    // 网络错误处理
    if (context === '网络请求' || message.includes('network')) {
      this.handleNetworkError(errorInfo);
    }
    
    // 登录错误处理
    if (context === '用户登录' || message.includes('login')) {
      this.handleLoginError(errorInfo);
    }
    
    // 权限错误处理
    if (message.includes('permission') || message.includes('unauthorized')) {
      this.handlePermissionError(errorInfo);
    }
  }

  /**
   * 处理网络错误
   * @param {Object} errorInfo 错误信息
   */
  handleNetworkError(errorInfo) {
    // 可以添加网络重试逻辑
    console.log('处理网络错误:', errorInfo.message);
  }

  /**
   * 处理登录错误
   * @param {Object} errorInfo 错误信息
   */
  handleLoginError(errorInfo) {
    // 清除登录状态，跳转到登录页面
    const app = getApp();
    if (app) {
      app.setGlobalData('isLoggedIn', false);
      app.setGlobalData('userInfo', null);
    }
    
    wx.navigateTo({
      url: '/pages/phone-login/phone-login'
    });
  }

  /**
   * 处理权限错误
   * @param {Object} errorInfo 错误信息
   */
  handlePermissionError(errorInfo) {
    wx.showModal({
      title: '权限不足',
      content: '您没有执行此操作的权限，请联系管理员',
      showCancel: false
    });
  }

  /**
   * 启动定时错误上报
   */
  startPeriodicReport() {
    setInterval(() => {
      this.reportErrors();
    }, this.reportInterval);
  }

  /**
   * 上报错误到服务器
   */
  async reportErrors() {
    if (this.isReporting || this.errorQueue.length === 0) {
      return;
    }
    
    this.isReporting = true;
    
    try {
      const errorsToReport = [...this.errorQueue];
      this.errorQueue = [];
      
      // 调用云函数上报错误
      await wx.cloud.callFunction({
        name: 'reportErrors',
        data: {
          errors: errorsToReport,
          timestamp: Date.now()
        }
      });
      
      console.log('✅ 已上报 ' + errorsToReport.length + ' 个错误');
    } catch (error) {
      console.error('错误上报失败:', error);
      // 上报失败时，将错误重新加入队列
      this.errorQueue.unshift(...errorsToReport);
    } finally {
      this.isReporting = false;
    }
  }

  /**
   * 生成错误ID
   * @returns {string} 错误ID
   */
  generateErrorId() {
    return 'error_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取用户代理信息
   * @returns {string} 用户代理信息
   */
  getUserAgent() {
    const systemInfo = wx.getSystemInfoSync();
    return systemInfo.platform + ' ' + systemInfo.system + ' WeChat/' + systemInfo.version;
  }

  /**
   * 获取应用版本
   * @returns {string} 应用版本
   */
  getAppVersion() {
    const app = getApp();
    return app ? app.getGlobalData('appVersion') || '1.0.0' : '1.0.0';
  }

  /**
   * 获取当前页面路径
   * @returns {string} 当前页面路径
   */
  getCurrentPage() {
    const pages = getCurrentPages();
    return pages.length > 0 ? pages[pages.length - 1].route : 'unknown';
  }

  /**
   * 异步操作包装器
   * @param {Function} asyncFn 异步函数
   * @param {string} context 错误上下文
   * @returns {Promise} 包装后的Promise
   */
  static async wrapAsync(asyncFn, context = '异步操作') {
    try {
      return await asyncFn();
    } catch (error) {
      this.handleError(context, error);
      throw error;
    }
  }

  /**
   * Promise包装器
   * @param {Promise} promise Promise对象
   * @param {string} context 错误上下文
   * @returns {Promise} 包装后的Promise
   */
  static wrapPromise(promise, context = 'Promise操作') {
    return promise.catch(error => {
      this.handleError(context, error);
      throw error;
    });
  }
}

// 导出错误处理器
module.exports = EnhancedErrorHandler;
