<!--pages/favorites/favorites.wxml-->
<!-- 收藏夹页面 -->

<view class="favorites-container">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="onBack">
      <image class="back-icon" src="/images/svg/arrow-left.svg" mode="aspectFit"></image>
    </view>
    <view class="nav-title">收藏夹</view>
    <view class="nav-right">
      <view class="nav-action" bindtap="onToggleSearch">
        <image class="action-icon" src="/images/search.png" mode="aspectFit"></image>
      </view>
      <view class="nav-action" bindtap="onToggleSort">
        <image class="action-icon" src="/images/filter.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar" wx:if="{{searchVisible}}">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/images/search.png" mode="aspectFit"></image>
      <input 
        class="search-input" 
        placeholder="搜索收藏内容"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
      />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="onSearchClear">
        <image class="clear-icon" src="/images/close.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-bar">
    <view class="stats-info">
      <text class="stats-text">共 {{totalCount}} 个收藏</text>
    </view>
    <view class="stats-actions">
      <view class="action-btn" bindtap="onToggleEdit">
        <text class="action-text">{{editMode ? '完成' : '编辑'}}</text>
      </view>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-container">
    <scroll-view class="tabs-scroll" scroll-x="true">
      <view class="tabs">
        <view 
          class="tab-item {{activeTab === index ? 'active' : ''}}"
          wx:for="{{tabs}}"
          wx:key="index"
          data-index="{{index}}"
          bindtap="onTabChange"
        >
          <text class="tab-text">{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 编辑模式工具栏 -->
  <view class="edit-toolbar" wx:if="{{editMode}}">
    <view class="toolbar-left">
      <view class="select-all" bindtap="onSelectAll">
        <text class="select-text">{{selectedItems.length === filteredFavorites.length && filteredFavorites.length > 0 ? '取消全选' : '全选'}}</text>
      </view>
    </view>
    <view class="toolbar-right">
      <view class="toolbar-btn delete" bindtap="onBatchDelete">
        <image class="btn-icon" src="/images/svg/slash.svg" mode="aspectFit"></image>
        <text class="btn-text">删除 ({{selectedItems.length}})</text>
      </view>
    </view>
  </view>

  <!-- 收藏列表 -->
  <scroll-view 
    class="favorites-list"
    scroll-y="true"
    refresher-enabled="{{!editMode}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="refreshFavorites"
    bindscrolltolower="loadMoreFavorites"
  >
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredFavorites.length === 0 && !loading}}">
      <image class="empty-image" src="{{emptyConfig.image}}" mode="aspectFit"></image>
      <view class="empty-title">{{emptyConfig.title}}</view>
      <view class="empty-subtitle">{{emptyConfig.subtitle}}</view>
    </view>

    <!-- 收藏项列表 -->
    <view class="favorite-items" wx:else>
      <view 
        class="favorite-item {{editMode ? 'edit-mode' : ''}} {{selectedItems.includes(item.id) ? 'selected' : ''}}"
        wx:for="{{filteredFavorites}}"
        wx:key="id"
        data-item="{{item}}"
        data-id="{{item.id}}"
        bindtap="onFavoriteItemTap"
      >
        <!-- 选择框 -->
        <view class="select-checkbox" wx:if="{{editMode}}">
          <view class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}">
            <image class="check-icon" src="/images/svg/check.svg" mode="aspectFit"></image>
          </view>
        </view>

        <!-- 美食收藏 -->
        <view class="food-favorite" wx:if="{{item.type === 'food'}}">
          <image class="food-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="food-info">
            <view class="food-title">{{item.title}}</view>
            <view class="food-desc">{{item.description}}</view>
            <view class="food-meta">
              <view class="food-rating">
                <image class="star-icon" src="/images/star.svg" mode="aspectFit"></image>
                <text class="rating-text">{{item.rating}}</text>
              </view>
              <view class="food-time">{{item.cookTime}}</view>
            </view>
            <view class="food-tags">
              <view class="tag" wx:for="{{item.tags}}" wx:key="index">
                <text class="tag-text">{{item}}</text>
              </view>
            </view>
          </view>
          <view class="favorite-actions" wx:if="{{!editMode}}">
            <view class="action-item" data-id="{{item.id}}" bindtap="onUnfavorite">
              <image class="action-icon" src="/images/svg/heart-broken.svg" mode="aspectFit"></image>
            </view>
            <view class="action-item" data-item="{{item}}" bindtap="onShareFavorite">
              <image class="action-icon" src="/images/share.svg" mode="aspectFit"></image>
            </view>
          </view>
        </view>

        <!-- 动态收藏 -->
        <view class="post-favorite" wx:elif="{{item.type === 'post'}}">
          <view class="post-header">
            <image class="author-avatar" src="{{item.author.avatar}}" mode="aspectFill"></image>
            <view class="author-info">
              <view class="author-name">{{item.author.name}}</view>
              <view class="post-time">{{formatTime(item.createTime)}}</view>
            </view>
          </view>
          <view class="post-content">
            <view class="post-title">{{item.title}}</view>
            <view class="post-text">{{item.content}}</view>
            <view class="post-images" wx:if="{{item.images && item.images.length > 0}}">
              <image 
                class="post-image"
                wx:for="{{item.images}}"
                wx:key="index"
                src="{{item}}"
                mode="aspectFill"
              ></image>
            </view>
          </view>
          <view class="post-stats">
            <view class="stat-item">
              <image class="stat-icon" src="/images/like.png" mode="aspectFit"></image>
              <text class="stat-text">{{item.likes}}</text>
            </view>
            <view class="stat-item">
              <image class="stat-icon" src="/images/comment.png" mode="aspectFit"></image>
              <text class="stat-text">{{item.comments}}</text>
            </view>
          </view>
          <view class="favorite-actions" wx:if="{{!editMode}}">
            <view class="action-item" data-id="{{item.id}}" bindtap="onUnfavorite">
              <image class="action-icon" src="/images/svg/heart-broken.svg" mode="aspectFit"></image>
            </view>
            <view class="action-item" data-item="{{item}}" bindtap="onShareFavorite">
              <image class="action-icon" src="/images/share.svg" mode="aspectFit"></image>
            </view>
          </view>
        </view>

        <!-- 用户收藏 -->
        <view class="user-favorite" wx:elif="{{item.type === 'user'}}">
          <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
          <view class="user-info">
            <view class="user-name">
              <text class="name-text">{{item.name}}</text>
              <image class="verified-icon" wx:if="{{item.verified}}" src="/images/svg/check.svg" mode="aspectFit"></image>
            </view>
            <view class="user-bio">{{item.bio}}</view>
            <view class="user-stats">
              <view class="user-stat">
                <text class="stat-number">{{item.followers}}</text>
                <text class="stat-label">粉丝</text>
              </view>
              <view class="user-stat">
                <text class="stat-number">{{item.posts}}</text>
                <text class="stat-label">动态</text>
              </view>
            </view>
          </view>
          <view class="favorite-actions" wx:if="{{!editMode}}">
            <view class="action-item" data-id="{{item.id}}" bindtap="onUnfavorite">
              <image class="action-icon" src="/images/svg/heart-broken.svg" mode="aspectFit"></image>
            </view>
            <view class="action-item" data-item="{{item}}" bindtap="onShareFavorite">
              <image class="action-icon" src="/images/share.svg" mode="aspectFit"></image>
            </view>
          </view>
        </view>

        <!-- 收藏时间 -->
        <view class="favorite-time">
          <text class="time-text">收藏于 {{formatTime(item.favoriteTime)}}</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && filteredFavorites.length > 0}}">
      <text class="no-more-text">没有更多了</text>
    </view>
  </scroll-view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>