// pages/feedback/feedback.js
// 建议反馈页面

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 反馈类型
    feedbackTypes: [
      { value: 'bug', name: 'Bug反馈', icon: '/images/svg/bug.svg', color: '#000000' }, // 统一为黑色
      { value: 'feature', name: '功能建议', icon: '/images/svg/bulb.svg', color: '#000000' }, // 统一为黑色
      { value: 'ui', name: '界面优化', icon: '/images/svg/design.svg', color: '#000000' }, // 统一为黑色
      { value: 'performance', name: '性能问题', icon: '/images/svg/speed.svg', color: '#000000' }, // 统一为黑色
      { value: 'content', name: '内容建议', icon: '/images/svg/edit.svg', color: '#000000' }, // 统一为黑色
      { value: 'other', name: '其他问题', icon: '/images/svg/more.svg', color: '#000000' } // 统一为黑色
    ],
    
    // 表单数据
    formData: {
      type: '',                    // 反馈类型
      title: '',                   // 反馈标题
      content: '',                 // 反馈内容
      contact: '',                 // 联系方式
      images: [],                  // 上传图片
      priority: 'medium',          // 优先级
      anonymous: false,            // 匿名反馈
      allowContact: true           // 允许联系
    },
    
    // 优先级选项
    priorityOptions: [
      { value: 'low', name: '低', color: '#666666' }, // 改为灰色
      { value: 'medium', name: '中', color: '#333333' }, // 改为深灰色
      { value: 'high', name: '高', color: '#000000' } // 改为黑色
    ],
    
    // 页面状态
    submitting: false,           // 提交中
    
    // 图片上传
    maxImages: 6,                // 最大图片数量
    
    // 字数统计
    titleMaxLength: 50,
    contentMaxLength: 500,
    
    // 历史反馈
    showHistory: false,
    historyList: [],
    
    // 常见问题
    showFAQ: false,
    faqList: [
      {
        question: '如何快速找到美食推荐？',
        answer: '您可以在首页使用搜索功能，或者根据分类浏览推荐内容。'
      },
      {
        question: '为什么无法发布动态？',
        answer: '请检查网络连接，确保已登录账户，并且内容符合社区规范。'
      },
      {
        question: '如何修改个人信息？',
        answer: '进入个人页面，点击个人信息即可编辑您的资料。'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有传入的反馈类型，自动选择
    if (options.type) {
      this.setData({
        'formData.type': options.type
      });
    }
    
    this.loadHistoryFeedback();
  },

  /**
   * 选择反馈类型
   */
  onSelectType(e) {
    const { type } = e.currentTarget.dataset;
    
    this.setData({
      'formData.type': type
    });
    
    // 根据类型提供建议
    this.showTypeSuggestion(type);
  },

  /**
   * 显示类型建议
   */
  showTypeSuggestion(type) {
    const suggestions = {
      'bug': '请详细描述问题出现的步骤和环境，这有助于我们快速定位问题。',
      'feature': '请描述您希望添加的功能和使用场景，我们会认真考虑您的建议。',
      'ui': '请说明具体的界面问题和改进建议，可以上传截图帮助说明。',
      'performance': '请描述性能问题的具体表现，如卡顿、加载慢等。',
      'content': '请提供具体的内容建议，如希望增加的内容类型等。'
    };
    
    if (suggestions[type]) {
      wx.showToast({
        title: suggestions[type],
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 输入标题
   */
  onTitleInput(e) {
    const title = e.detail.value;
    this.setData({
      'formData.title': title
    });
  },

  /**
   * 输入内容
   */
  onContentInput(e) {
    const content = e.detail.value;
    this.setData({
      'formData.content': content
    });
  },

  /**
   * 输入联系方式
   */
  onContactInput(e) {
    const contact = e.detail.value;
    this.setData({
      'formData.contact': contact
    });
  },

  /**
   * 选择优先级
   */
  onSelectPriority(e) {
    const { priority } = e.currentTarget.dataset;
    this.setData({
      'formData.priority': priority
    });
  },

  /**
   * 切换匿名反馈
   */
  onToggleAnonymous(e) {
    const anonymous = e.detail.value;
    this.setData({
      'formData.anonymous': anonymous
    });
    
    // 如果选择匿名，清空联系方式
    if (anonymous) {
      this.setData({
        'formData.contact': '',
        'formData.allowContact': false
      });
    }
  },

  /**
   * 切换允许联系
   */
  onToggleContact(e) {
    const allowContact = e.detail.value;
    this.setData({
      'formData.allowContact': allowContact
    });
  },

  /**
   * 选择图片
   */
  onChooseImage() {
    const { images, maxImages } = this.data.formData;
    const remainCount = maxImages - images.length;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: `最多只能上传${maxImages}张图片`,
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = [...images, ...res.tempFilePaths];
        this.setData({
          'formData.images': newImages
        });
      },
      fail: () => {
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data.formData;
    
    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  /**
   * 删除图片
   */
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data.formData;
    
    wx.showModal({
      title: '删除图片',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          const newImages = images.filter((_, i) => i !== index);
          this.setData({
            'formData.images': newImages
          });
        }
      }
    });
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { type, title, content } = this.data.formData;
    
    if (!type) {
      wx.showToast({
        title: '请选择反馈类型',
        icon: 'none'
      });
      return false;
    }
    
    if (!title.trim()) {
      wx.showToast({
        title: '请输入反馈标题',
        icon: 'none'
      });
      return false;
    }
    
    if (title.length > this.data.titleMaxLength) {
      wx.showToast({
        title: `标题不能超过${this.data.titleMaxLength}个字符`,
        icon: 'none'
      });
      return false;
    }
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return false;
    }
    
    if (content.length > this.data.contentMaxLength) {
      wx.showToast({
        title: `内容不能超过${this.data.contentMaxLength}个字符`,
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 提交反馈
   */
  onSubmitFeedback() {
    if (!this.validateForm()) {
      return;
    }
    
    this.setData({ submitting: true });
    
    // 模拟提交反馈
    setTimeout(() => {
      this.submitFeedbackToServer();
    }, 1000);
  },

  /**
   * 提交到服务器
   */
  async submitFeedbackToServer() {
    try {
      const { formData } = this.data;
      
      // 模拟上传图片
      const uploadedImages = await this.uploadImages(formData.images);
      
      // 构建提交数据
      const submitData = {
        ...formData,
        images: uploadedImages,
        submitTime: new Date().toISOString(),
        deviceInfo: this.getDeviceInfo(),
        appVersion: app.globalData.version || '1.0.0'
      };
      
      // 模拟API调用
      console.log('提交反馈数据:', submitData);
      
      // 保存到本地历史
      this.saveFeedbackToHistory(submitData);
      
      this.setData({ submitting: false });
      
      wx.showModal({
        title: '提交成功',
        content: '感谢您的反馈！我们会认真处理您的建议。',
        showCancel: false,
        success: () => {
          this.resetForm();
        }
      });
      
    } catch (error) {
      this.setData({ submitting: false });
      
      wx.showModal({
        title: '提交失败',
        content: '网络异常，请稍后重试。',
        showCancel: false
      });
    }
  },

  /**
   * 上传图片
   */
  uploadImages(images) {
    return new Promise((resolve) => {
      // 模拟图片上传
      const uploadedUrls = images.map((image, index) => {
        return `https://example.com/feedback/image_${Date.now()}_${index}.jpg`;
      });
      
      setTimeout(() => {
        resolve(uploadedUrls);
      }, 500);
    });
  },

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    const systemInfo = wx.getSystemInfoSync();
    return {
      platform: systemInfo.platform,
      system: systemInfo.system,
      version: systemInfo.version,
      model: systemInfo.model,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight
    };
  },

  /**
   * 保存到历史记录
   */
  saveFeedbackToHistory(feedbackData) {
    const historyList = wx.getStorageSync('feedbackHistory') || [];
    
    const historyItem = {
      id: Date.now(),
      type: feedbackData.type,
      title: feedbackData.title,
      submitTime: feedbackData.submitTime,
      status: 'pending' // pending, processing, resolved
    };
    
    historyList.unshift(historyItem);
    
    // 只保留最近20条记录
    if (historyList.length > 20) {
      historyList.splice(20);
    }
    
    wx.setStorageSync('feedbackHistory', historyList);
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      'formData.type': '',
      'formData.title': '',
      'formData.content': '',
      'formData.contact': '',
      'formData.images': [],
      'formData.priority': 'medium',
      'formData.anonymous': false,
      'formData.allowContact': true
    });
  },

  /**
   * 加载历史反馈
   */
  loadHistoryFeedback() {
    const historyList = wx.getStorageSync('feedbackHistory') || [];
    this.setData({ historyList });
  },

  /**
   * 显示历史反馈
   */
  onShowHistory() {
    this.setData({ showHistory: true });
    this.loadHistoryFeedback();
  },

  /**
   * 隐藏历史反馈
   */
  onHideHistory() {
    this.setData({ showHistory: false });
  },

  /**
   * 查看历史详情
   */
  onViewHistoryDetail(e) {
    const { item } = e.currentTarget.dataset;
    
    wx.showModal({
      title: item.title,
      content: `提交时间：${new Date(item.submitTime).toLocaleString()}\n状态：${this.getStatusText(item.status)}`,
      showCancel: false
    });
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'pending': '待处理',
      'processing': '处理中',
      'resolved': '已解决'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 显示常见问题
   */
  onShowFAQ() {
    this.setData({ showFAQ: true });
  },

  /**
   * 隐藏常见问题
   */
  onHideFAQ() {
    this.setData({ showFAQ: false });
  },

  /**
   * 联系客服
   */
  onContactService() {
    wx.showActionSheet({
      itemList: ['复制客服邮箱', '拨打客服电话', '在线客服'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.setClipboardData({
              data: '<EMAIL>',
              success: () => {
                wx.showToast({
                  title: '邮箱已复制',
                  icon: 'success'
                });
              }
            });
            break;
          case 1:
            wx.makePhoneCall({
              phoneNumber: '************'
            });
            break;
          case 2:
            wx.showToast({
              title: '在线客服功能开发中',
              icon: 'none'
            });
            break;
        }
      }
    });
  },

  /**
   * 获取反馈类型名称
   */
  getTypeName(type) {
    const typeItem = this.data.feedbackTypes.find(item => item.value === type);
    return typeItem ? typeItem.name : type;
  },

  /**
   * 获取优先级名称
   */
  getPriorityName(priority) {
    const priorityItem = this.data.priorityOptions.find(item => item.value === priority);
    return priorityItem ? priorityItem.name : priority;
  }
});