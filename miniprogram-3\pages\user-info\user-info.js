// pages/user-info/user-info.js
// 个人信息编辑页面

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      nickName: '张三',
      avatarUrl: '/images/default-avatar.png',
      gender: 1, // 1-男性 2-女性 0-未知
      birthday: '1990-01-01',
      phone: '138****8888',
      email: '<EMAIL>',
      location: '北京市朝阳区',
      signature: '今天吃什么，让我来帮你决定！',
      realName: '',
      idCard: ''
    },
    genderOptions: [
      { value: 0, label: '保密' },
      { value: 1, label: '男' },
      { value: 2, label: '女' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    // 从全局数据或本地存储加载用户信息
    const globalUserInfo = app.globalData.userInfo;
    if (globalUserInfo) {
      this.setData({
        userInfo: { ...this.data.userInfo, ...globalUserInfo }
      });
    }
  },

  /**
   * 编辑头像
   */
  onEditAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.setData({
          'userInfo.avatarUrl': tempFilePath
        });
        wx.showToast({
          title: '头像已更新',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('选择头像失败:', err);
      }
    });
  },

  /**
   * 编辑昵称
   */
  onEditNickName() {
    wx.showModal({
      title: '修改昵称',
      editable: true,
      placeholderText: '请输入新昵称',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          this.setData({
            'userInfo.nickName': res.content.trim()
          });
          wx.showToast({
            title: '昵称已更新',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 性别选择更新
   */
  onGenderChange(e) {
    const index = e.detail.value;
    this.setData({
      'userInfo.gender': this.data.genderOptions[index].value
    });
  },

  /**
   * 生日选择更新
   */
  onDateChange(e) {
    this.setData({
      'userInfo.birthday': e.detail.value
    });
  },

  

  /**
   * 编辑手机号
   */
  onEditPhone() {
    wx.showModal({
      title: '修改手机号',
      editable: true,
      placeholderText: '请输入手机号',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          const phone = res.content.trim();
          // 简单的手机号验证
          if (!/^1[3-9]\d{9}$/.test(phone)) {
            wx.showToast({
              title: '手机号格式不正确',
              icon: 'none'
            });
            return;
          }
          this.setData({
            'userInfo.phone': phone
          });
          wx.showToast({
            title: '手机号已更新',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 编辑邮箱
   */
  onEditEmail() {
    wx.showModal({
      title: '修改邮箱',
      editable: true,
      placeholderText: '请输入邮箱地址',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          const email = res.content.trim();
          // 简单的邮箱验证
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            wx.showToast({
              title: '邮箱格式不正确',
              icon: 'none'
            });
            return;
          }
          this.setData({
            'userInfo.email': email
          });
          wx.showToast({
            title: '邮箱已更新',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 编辑地址
   */
  onEditLocation() {
    wx.showModal({
      title: '修改地址',
      editable: true,
      placeholderText: '请输入地址',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          this.setData({
            'userInfo.location': res.content.trim()
          });
          wx.showToast({
            title: '地址已更新',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 编辑个性签名
   */
  onEditSignature() {
    wx.showModal({
      title: '修改个性签名',
      editable: true,
      placeholderText: '请输入个性签名',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'userInfo.signature': res.content || ''
          });
          wx.showToast({
            title: '个性签名已更新',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 保存用户信息
   */
  onSave() {
    wx.showLoading({
      title: '保存中...'
    });

    // 模拟保存到服务器
    setTimeout(() => {
      wx.hideLoading();
      
      // 更新全局用户信息
      app.globalData.userInfo = { ...app.globalData.userInfo, ...this.data.userInfo };
      
      // 保存到本地存储
      wx.setStorageSync('userInfo', this.data.userInfo);
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  }
});