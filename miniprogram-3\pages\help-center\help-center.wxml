<!--pages/help-center/help-center.wxml-->
<!-- 帮助中心页面 - 黑白极简设计 -->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="onBack">
      <text class="nav-back-icon">←</text>
    </view>
    <view class="nav-title">帮助中心</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input class="search-input" 
             placeholder="搜索问题或关键词"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"/>
      <view wx:if="{{searchKeyword}}" class="search-clear" bindtap="onClearSearch">
        <text class="clear-icon">×</text>
      </view>
    </view>
  </view>

  <!-- 热门问题 -->
  <view wx:if="{{!searchKeyword}}" class="hot-section">
    <view class="section-title">
      <text class="title-icon">🔥</text>
      <text class="title-text">热门问题</text>
    </view>
    <view class="hot-questions">
      <view wx:for="{{hotQuestions}}" 
            wx:key="id" 
            class="hot-item"
            bindtap="onHotQuestionTap"
            data-id="{{item.id}}">
        <view class="hot-number">{{index + 1}}</view>
        <view class="hot-question">{{item.question}}</view>
        <view class="hot-count">{{item.viewCount}}次浏览</view>
      </view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="categories-container">
    <scroll-view class="categories" scroll-x="true">
      <view wx:for="{{categories}}" 
            wx:key="key" 
            class="category-item {{activeCategory === item.key ? 'active' : ''}}"
            bindtap="onCategoryChange"
            data-category="{{item.key}}">
        <text class="category-icon">{{item.icon}}</text>
        <text class="category-label">{{item.label}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 帮助条目列表 -->
  <view class="help-list">
    <!-- 搜索结果提示 -->
    <view wx:if="{{searchKeyword && filteredItems.length === 0}}" class="no-results">
      <view class="no-results-icon">🔍</view>
      <view class="no-results-title">未找到相关问题</view>
      <view class="no-results-desc">试试其他关键词或浏览分类问题</view>
    </view>

    <!-- 帮助条目 -->
    <view wx:for="{{filteredItems}}" 
          wx:key="id" 
          class="help-item"
          id="help-item-{{item.id}}">
      <view class="help-header" bindtap="onToggleItem" data-id="{{item.id}}">
        <view class="help-question">
          <text class="question-text">{{item.question}}</text>
          <view class="question-meta">
            <text class="category-tag">{{getCategoryName(item.category)}}</text>
            <text class="view-count">{{item.viewCount}}次浏览</text>
          </view>
        </view>
        <view class="help-toggle">
          <text class="toggle-icon {{expandedItems.indexOf(item.id) > -1 ? 'expanded' : ''}}">▼</text>
        </view>
      </view>
      
      <!-- 答案内容 -->
      <view wx:if="{{expandedItems.indexOf(item.id) > -1}}" class="help-answer">
        <view class="answer-content">{{item.answer}}</view>
        <view class="answer-tags">
          <text wx:for="{{item.tags}}" 
                wx:for-item="tag" 
                wx:key="*this" 
                class="answer-tag">{{tag}}</text>
        </view>
        <view class="answer-actions">
          <view class="action-item">
            <text class="action-icon">👍</text>
            <text class="action-text">有帮助</text>
          </view>
          <view class="action-item">
            <text class="action-icon">👎</text>
            <text class="action-text">没帮助</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系客服 -->
  <view class="contact-section">
    <view class="section-title">
      <text class="title-icon">📞</text>
      <text class="title-text">联系我们</text>
    </view>
    <view class="contact-info">
      <view class="contact-item" bindtap="onContactSupport" data-type="email">
        <view class="contact-icon">📧</view>
        <view class="contact-content">
          <view class="contact-label">邮箱支持</view>
          <view class="contact-value">{{contactInfo.email}}</view>
        </view>
        <view class="contact-arrow">→</view>
      </view>
      
      <view class="contact-item" bindtap="onContactSupport" data-type="phone">
        <view class="contact-icon">📱</view>
        <view class="contact-content">
          <view class="contact-label">电话支持</view>
          <view class="contact-value">{{contactInfo.phone}}</view>
        </view>
        <view class="contact-arrow">→</view>
      </view>
      
      <view class="contact-item" bindtap="onContactSupport" data-type="feedback">
        <view class="contact-icon">💬</view>
        <view class="contact-content">
          <view class="contact-label">意见反馈</view>
          <view class="contact-value">提交问题和建议</view>
        </view>
        <view class="contact-arrow">→</view>
      </view>
    </view>
    
    <view class="work-time">
      <text class="work-time-icon">⏰</text>
      <text class="work-time-text">{{contactInfo.workTime}}</text>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>