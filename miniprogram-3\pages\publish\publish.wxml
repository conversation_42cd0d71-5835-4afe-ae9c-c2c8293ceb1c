<!--pages/publish/publish.wxml-->
<!-- 发布页面 - Publish Page -->
<view class="publish-container">
  <!-- 顶部导航栏 - Top Navigation -->
  <view class="nav-bar">
    <text class="nav-title">发布动态</text>
  </view>

  <!-- 内容区域 - Content Area -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">
    <!-- 文本输入区域 - Text Input Area -->
    <view class="text-section">
      <textarea
        class="text-input"
        placeholder="分享你的美食体验..."
        placeholder-class="placeholder-style"
        value="{{content}}"
        bindinput="onContentInput"
        maxlength="{{maxContentLength}}"
        auto-height="true"
        show-confirm-bar="false"
        cursor-spacing="20"
        adjust-position="true"
      />
      <view class="char-count">{{content.length}}/{{maxContentLength}}</view>
    </view>

    <!-- 图片上传区域 - Image Upload Area -->
    <view class="image-section">
      <text class="section-title">添加图片</text>
      <view class="image-grid">
        <!-- 已选择的图片 - Selected Images -->
        <view class="image-item" wx:for="{{selectedImages}}" wx:key="index">
          <image
            class="preview-image"
            src="{{item}}"
            mode="aspectFill"
            bindtap="onPreviewImage"
            data-index="{{index}}"
            lazy-load="true"
          />
          <button
            class="delete-btn"
            bindtap="onDeleteImage"
            data-index="{{index}}"
            aria-label="删除图片"
          >
            <image class="delete-icon" src="/images/close.png" mode="aspectFit"/>
          </button>
        </view>

        <!-- 添加图片按钮 - Add Image Button -->
        <button
          class="add-image-btn"
          bindtap="onChooseImage"
          wx:if="{{selectedImages.length < maxImageCount}}"
          aria-label="添加图片"
        >
          <image class="add-icon" src="/images/add-image.svg" mode="aspectFit"/>
          <text class="add-text">添加图片</text>
        </button>
      </view>
      <view class="image-tip" style="height: 32rpx; display: block; box-sizing: border-box">最多可添加{{maxImageCount}}张图片</view>
    </view>

    <!-- 位置选择区域 - Location Selection Area -->
    <view class="location-section">
      <text class="section-title">添加位置</text>
      <button class="location-btn" bindtap="onChooseLocation">
        <image class="location-icon" src="/images/location.svg" mode="aspectFit"/>
        <view class="location-content">
          <text class="location-text" wx:if="{{!selectedLocation}}">选择位置</text>
          <view wx:else>
            <text class="location-name">{{selectedLocation.name}}</text>
            <text class="location-address">{{selectedLocation.address}}</text>
          </view>
        </view>
        <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"/>
      </button>
    </view>

    <!-- 分类标签选择区域 - Category Tags Selection Area -->
    <view class="category-section">
      <text class="section-title">选择分类</text>
      <view class="category-grid">
        <button 
          class="category-tag {{selectedCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}" 
          wx:key="id"
          bindtap="onCategorySelect"
          data-category="{{item.id}}"
        >
          <text class="category-text">{{item.name}}</text>
        </button>
      </view>
    </view>

    <!-- 其他设置区域 - Other Settings Area -->
    <view class="settings-section">
      <view class="setting-item">
        <text class="setting-label">同步到朋友圈</text>
        <switch class="setting-switch" checked="{{syncToTimeline}}" bindchange="onSyncChange"/>
      </view>
      <view class="setting-item">
        <text class="setting-label">允许评论</text>
        <switch class="setting-switch" checked="{{allowComment}}" bindchange="onCommentChange"/>
      </view>
    </view>

    <!-- 底部安全区域 - Bottom Safe Area -->
    <view class="safe-area"></view>
  </scroll-view>

  <!-- 错误提示 - Error Message -->
  <view wx:if="{{hasError}}" class="error-container">
    <view class="error-content">
      <view class="error-icon">⚠️</view>
      <text class="error-message">{{errorMessage}}</text>
      <button class="retry-btn" bindtap="onRetryPublish">重试发布</button>
    </view>
  </view>

  <!-- 底部操作栏 - Bottom Action Bar -->
  <view class="bottom-action-bar">
    <button class="action-btn cancel-btn" bindtap="onCancelTap">
      <text class="btn-text">取消</text>
    </button>
    <button class="action-btn publish-btn {{canPublish ? 'active' : 'disabled'}}" bindtap="onPublishSubmit" disabled="{{!canPublish || isPublishing}}">
      <text class="btn-text" wx:if="{{isPublishing}}">发布中...</text>
      <text class="btn-text" wx:else>发布</text>
    </button>
  </view>

  <!-- 加载遮罩 - Loading Overlay -->
  <view class="loading-overlay" wx:if="{{isPublishing}}">
    <view class="loading-content">
      <image class="loading-icon" src="images/loading.gif" mode="aspectFit"/>
      <text class="loading-text">发布中...</text>
    </view>
  </view>
</view>
