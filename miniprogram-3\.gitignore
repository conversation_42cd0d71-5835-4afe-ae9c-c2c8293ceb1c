# 依赖目录
node_modules/
backend/node_modules/

# 构建输出
dist/
build/
coverage/

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 上传文件
uploads/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE 配置
.vscode/
.idea/
*.swp
*.swo

# 测试报告和结果
coverage/
test-results/

# 文档和说明文件（开发过程中的临时文档）
*.md
*.txt
*.html
roles/
docs/

# 脚本和工具
scripts/
web-preview/

# 后端服务（小程序不需要）
backend/

# 云函数（如果使用云开发则移除此行）
cloudfunctions/

# 模拟数据
mock/

# 调试和开发工具
.debug-env
.trae/
.codebuddy/
test-login.js

# SVG 设计文件（排除图标文件）
*.svg
!images/*.svg
!images/**/*.svg

# 预览文件
*.html

# 包管理文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 微信开发者工具生成的文件
project.private.config.json