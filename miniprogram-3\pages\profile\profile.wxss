/* pages/profile/profile.wxss */
/* 统一设计风格 - 与应用整体保持一致 */

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: #F0F2F5;
  color: #333333;
  line-height: 1.6;
}

.container {
  max-width: 750rpx;
  margin: 0 auto;
  background-color: #F0F2F5;
  min-height: 100vh;
  padding: 32rpx;
}

/* 顶部用户信息区域 */
.user-header {
  background: #ffffff;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: #333333;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
}

.user-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 2rpx;
  background: #E0E0E0;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 2rpx solid #E0E0E0;
  margin-bottom: 24rpx;
  background: #ffffff;
}

.user-name {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333333;
}

.user-id {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

.user-stats {
  display: flex;
  justify-content: center;
  gap: 60rpx;
  margin-top: 24rpx;
}

.stat-item {
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.stat-item:hover {
  background-color: #F0F2F5;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
  color: #333333;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
  text-transform: uppercase;
}

/* 主要功能区域 */
.main-content {
  padding: 0;
}

.section {
  background: #ffffff;
  margin-bottom: 32rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-title {
  padding: 24rpx 32rpx 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #F0F2F5;
  background-color: #FAFAFA;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F2F5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.menu-item:hover {
  background-color: #F8F9FA;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: #000000; /* 改为黑色，符合极简风格 */
  transition: width 0.2s ease;
}

.menu-item:hover::before {
  width: 4rpx;
}

.menu-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  background: #F8F9FA;
  color: #666666;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.menu-subtitle {
  font-size: 24rpx;
  color: #999999;
  margin-top: 2rpx;
}

.menu-arrow {
  color: #CCCCCC;
  font-size: 28rpx;
  font-weight: 300;
}

.menu-badge {
  background: #000000; /* 改为黑色背景 */
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

/* 快捷操作网格 */
.quick-actions {
  margin-bottom: 32rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.action-card {
  background: #ffffff;
  border: 1rpx solid #E0E0E0;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border-color: #000000; /* 改为黑色边框 */
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  background: #000000; /* 改为纯黑色，去除渐变 */
  transition: height 0.2s ease;
}

.action-card:hover::before {
  height: 4rpx;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.action-subtitle {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
}

/* 退出按钮 - 黑白极简风格 */
.logout-section {
  background: #ffffff;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.logout-btn {
  width: 100%;
  padding: 28rpx;
  background: #f8f8f8; /* 改为浅灰色背景 */
  border: 2rpx solid #e0e0e0; /* 添加边框 */
  border-radius: 16rpx;
  color: #666666; /* 改为深灰色文字 */
  font-size: 30rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn::after {
  border: none; /* 移除默认边框 */
}

.logout-btn:active {
  background: #f0f0f0; /* 按压时的浅灰色 */
  border-color: #d0d0d0;
  transform: scale(0.98); /* 微妙的缩放效果 */
}

/* 底部安全区域 */
.safe-area {
  height: 68rpx;
  background: #F0F2F5;
}

/* ==================== 未登录状态样式 ==================== */

/* 登录容器 */
.login-container {
  max-width: 750rpx;
  margin: 0 auto;
  background-color: #F0F2F5;
  min-height: 100vh;
  padding: 32rpx;
}

/* 登录引导头部 */
.login-header {
  background: #ffffff;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  padding: 80rpx 40rpx 60rpx;
  text-align: center;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 2rpx solid #E0E0E0;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.login-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.login-subtitle {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 功能介绍区域 */
.login-features {
  background: #ffffff;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  background: #F8F8F8;
  border: 1rpx solid #E0E0E0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666666;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

/* 登录操作区域 */
.login-actions {
  background: #ffffff;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  text-align: center;
}

.login-btn {
  width: 100%;
  padding: 32rpx;
  background: #ffffff;
  border: 2rpx solid #000000;
  border-radius: 16rpx;
  color: #000000;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
  transition: all 0.2s ease;
}

.login-btn::after {
  border: none;
}

.login-btn:active {
  background: #f8f8f8;
  border-color: #333333;
  transform: scale(0.98);
}

/* 微信登录按钮样式 */
.wechat-btn {
  background: #ffffff;
  border: 2rpx solid #000000;
  color: #000000;
}

.wechat-btn:active {
  background: #f8f8f8;
  border-color: #333333;
}

/* 手机登录按钮样式 */
.phone-btn {
  background: #ffffff;
  border: 2rpx solid #000000;
  color: #000000;
}

.phone-btn:active {
  background: #f8f8f8;
  border-color: #333333;
}

.login-tips {
  font-size: 22rpx;
  color: #999999;
  line-height: 1.4;
}

/* 游客模式区域 */
.guest-section {
  background: #ffffff;
  border: 1rpx solid #E0E0E0;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.guest-title {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.guest-menu {
  display: flex;
  justify-content: space-around;
}

.guest-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.guest-item:active {
  transform: scale(0.95);
  opacity: 0.7;
}

.guest-icon {
  width: 48rpx;
  height: 48rpx;
  background: #F8F8F8;
  border: 1rpx solid #E0E0E0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.guest-text {
  font-size: 24rpx;
  color: #666666;
}
