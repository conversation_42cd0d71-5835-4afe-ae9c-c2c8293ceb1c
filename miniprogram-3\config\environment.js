/**
 * 小程序前端环境配置管理
 * 根据不同环境ID配置API地址和其他环境相关设置
 */

// 环境配置映射
const ENVIRONMENT_CONFIG = {
  // 开发环境 - 本地后端
  'dev': {
    name: '开发环境',
    apiBaseUrl: 'http://localhost:3000/api/v1',
    wxAppId: 'wxd647e87260152f82',
    debug: true,
    enableMock: true,
    logLevel: 'debug',
    // 开发环境特殊配置
    enableVConsole: true,
    enablePerformanceMonitor: false,
    apiTimeout: 10000,
    // 云开发配置
    useCloudFunction: false,
    cloudEnvId: null
  },
  
  // 云开发环境
  'cloud-dev': {
    name: '云开发环境',
    apiBaseUrl: null, // 云函数不需要API地址
    wxAppId: 'wxd647e87260152f82',
    debug: true,
    enableMock: false,
    logLevel: 'debug',
    // 云开发特殊配置
    enableVConsole: true,
    enablePerformanceMonitor: true,
    apiTimeout: 15000,
    // 云开发配置
    useCloudFunction: true,
    cloudEnvId: 'cloud1-7gyu22znd8046aec' // 修正环境ID：znd而不是rnd
  },
  
  // 云开发生产环境
  'cloud-prod': {
    name: '云开发生产环境',
    apiBaseUrl: null, // 云函数不需要API地址
    wxAppId: 'wx_production_appid_replace_this',
    debug: false,
    enableMock: false,
    logLevel: 'error',
    // 云开发特殊配置
    enableVConsole: false,
    enablePerformanceMonitor: true,
    apiTimeout: 10000,
    // 云开发配置
    useCloudFunction: true,
    cloudEnvId: 'cloud1-7gyu22znd8046aec' // 修正环境ID：znd而不是rnd
  },
  
  // 测试环境
  'test': {
    name: '测试环境',
    apiBaseUrl: 'https://test-api.yourapp.com/api/v1',
    wxAppId: 'wxd647e87260152f82',
    debug: true,
    enableMock: false,
    logLevel: 'info',
    // 测试环境特殊配置
    enableVConsole: true,
    enablePerformanceMonitor: true,
    apiTimeout: 8000,
    // 云开发配置
    useCloudFunction: false,
    cloudEnvId: null
  },
  
  // 预生产环境
  'staging': {
    name: '预生产环境',
    apiBaseUrl: 'https://staging-api.yourapp.com/api/v1',
    wxAppId: 'wx_staging_appid_replace_this',
    debug: false,
    enableMock: false,
    logLevel: 'warn',
    // 预生产环境特殊配置
    enableVConsole: false,
    enablePerformanceMonitor: true,
    apiTimeout: 6000,
    // 云开发配置
    useCloudFunction: false,
    cloudEnvId: null
  },
  
  // 生产环境
  'prod': {
    name: '生产环境',
    apiBaseUrl: 'https://api.yourapp.com/api/v1',
    wxAppId: 'wx_production_appid_replace_this',
    debug: false,
    enableMock: false,
    logLevel: 'error',
    // 生产环境特殊配置
    enableVConsole: false,
    enablePerformanceMonitor: true,
    apiTimeout: 5000,
    // 云开发配置
    useCloudFunction: false,
    cloudEnvId: null
  }
};

/**
 * 获取当前环境ID
 * 小程序中通过编译时配置或运行时检测确定环境
 */
function getCurrentEnvironmentId() {
  // 优先从编译时配置获取
  if (typeof __ENV_ID__ !== 'undefined') {
    return __ENV_ID__;
  }
  
  // 从本地存储获取（用于开发调试）
  try {
    let envId;
    if (typeof wx !== 'undefined' && wx.getStorageSync) {
      // 小程序环境
      envId = wx.getStorageSync('__debug_env_id__');
    } else {
      // Node.js环境，从文件读取
      try {
        const fs = require('fs');
        const path = require('path');
        const envFile = path.join(__dirname, '..', '.debug-env');
        if (fs.existsSync(envFile)) {
          envId = fs.readFileSync(envFile, 'utf8').trim();
        }
      } catch (fsError) {
        // 文件不存在或读取失败，忽略
      }
    }
    if (envId) {
      return envId;
    }
  } catch (error) {
    console.warn('获取调试环境ID失败:', error);
  }
  
  // 根据域名判断环境（如果是H5版本）
  if (typeof window !== 'undefined' && window.location) {
    const hostname = window.location.hostname;
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
      return 'dev';
    } else if (hostname.includes('test')) {
      return 'test';
    } else if (hostname.includes('staging')) {
      return 'staging';
    } else {
      return 'prod';
    }
  }
  
  // 默认开发环境
  return 'dev';
}

/**
 * 获取环境配置
 * @param {string} envId - 环境ID，不传则使用当前环境
 * @returns {object} 环境配置对象
 */
function getEnvironmentConfig(envId = null) {
  const currentEnvId = envId || getCurrentEnvironmentId();
  
  if (!ENVIRONMENT_CONFIG[currentEnvId]) {
    console.error(`未知的环境ID: ${currentEnvId}，使用开发环境配置`);
    return {
      id: 'dev',
      ...ENVIRONMENT_CONFIG['dev']
    };
  }
  
  return {
    id: currentEnvId,
    ...ENVIRONMENT_CONFIG[currentEnvId]
  };
}

/**
 * 设置调试环境ID（仅在开发模式下使用）
 * @param {string} envId - 环境ID
 * @returns {boolean} 是否设置成功
 */
function setDebugEnvironmentId(envId) {
  if (!ENVIRONMENT_CONFIG[envId]) {
    console.error(`无效的环境ID: ${envId}`);
    return false;
  }
  
  try {
    // 检查是否在小程序环境中
    if (typeof wx !== 'undefined' && wx.setStorageSync) {
      wx.setStorageSync('__debug_env_id__', envId);
    } else {
      // Node.js环境中，使用文件系统存储
      const fs = require('fs');
      const path = require('path');
      const envFile = path.join(__dirname, '..', '.debug-env');
      fs.writeFileSync(envFile, envId, 'utf8');
    }
    console.log(`已设置调试环境ID: ${envId}`);
    return true;
  } catch (error) {
    console.error('设置调试环境ID失败:', error);
    return false;
  }
}

/**
 * 清除调试环境ID
 */
function clearDebugEnvironmentId() {
  try {
    if (typeof wx !== 'undefined' && wx.removeStorageSync) {
      // 小程序环境
      wx.removeStorageSync('__debug_env_id__');
    } else {
      // Node.js环境，删除文件
      const fs = require('fs');
      const path = require('path');
      const envFile = path.join(__dirname, '..', '.debug-env');
      if (fs.existsSync(envFile)) {
        fs.unlinkSync(envFile);
      }
    }
    console.log('已清除调试环境ID');
  } catch (error) {
    console.error('清除调试环境ID失败:', error);
  }
}

/**
 * 获取所有可用环境
 * @returns {array} 环境列表
 */
function getAvailableEnvironments() {
  return Object.keys(ENVIRONMENT_CONFIG).map(id => ({
    id,
    name: ENVIRONMENT_CONFIG[id].name
  }));
}

/**
 * 初始化环境配置
 * 在app.js中调用，设置全局环境配置
 */
function initializeEnvironment() {
  const config = getEnvironmentConfig();
  
  // 设置全局配置
  if (typeof getApp === 'function') {
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.environment = config;
      app.globalData.apiBaseUrl = config.apiBaseUrl;
    }
  }
  
  // 开发环境启用vConsole
  if (config.enableVConsole && config.debug) {
    // 这里可以引入vConsole或其他调试工具
    console.log('开发环境已启用调试模式');
  }
  
  console.log(`当前环境: ${config.name} (${config.id})`);
  console.log(`API地址: ${config.apiBaseUrl}`);
  
  return config;
}

module.exports = {
  ENVIRONMENT_CONFIG,
  getCurrentEnvironmentId,
  getEnvironmentConfig,
  setDebugEnvironmentId,
  clearDebugEnvironmentId,
  getAvailableEnvironments,
  initializeEnvironment
};