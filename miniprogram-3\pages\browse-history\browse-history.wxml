<!--pages/browse-history/browse-history.wxml-->
<!-- 浏览历史页面 -->

<view class="history-container">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="onBack">
      <image class="back-icon" src="/images/svg/arrow-left.svg" mode="aspectFit"></image>
    </view>
    <view class="nav-title">浏览历史</view>
    <view class="nav-right">
      <view class="nav-action" bindtap="onToggleSearch">
        <image class="action-icon" src="/images/search.png" mode="aspectFit"></image>
      </view>
      <view class="nav-action" bindtap="onSetFilter">
        <image class="action-icon" src="/images/filter.svg" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar" wx:if="{{searchVisible}}">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/images/search.png" mode="aspectFit"></image>
      <input 
        class="search-input" 
        placeholder="搜索浏览记录"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
      />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="onSearchClear">
        <image class="clear-icon" src="/images/close.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-number">{{totalCount}}</view>
        <view class="stat-label">总记录</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{todayCount}}</view>
        <view class="stat-label">今日浏览</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.weekViews}}</view>
        <view class="stat-label">本周浏览</view>
      </view>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <view class="action-left">
      <view class="filter-info">
        <text class="filter-text">{{filterType === 'all' ? '全部类型' : filterType === 'food' ? '美食' : filterType === 'post' ? '动态' : '用户'}}</text>
      </view>
    </view>
    <view class="action-right">
      <view class="action-btn" bindtap="onToggleEdit">
        <text class="action-text">{{editMode ? '完成' : '编辑'}}</text>
      </view>
      <view class="action-btn danger" bindtap="onClearAll">
        <text class="action-text">清空</text>
      </view>
    </view>
  </view>

  <!-- 编辑模式工具栏 -->
  <view class="edit-toolbar" wx:if="{{editMode}}">
    <view class="toolbar-left">
      <view class="select-all" bindtap="onSelectAll">
        <text class="select-text">{{selectedItems.length === totalCount && totalCount > 0 ? '取消全选' : '全选'}}</text>
      </view>
    </view>
    <view class="toolbar-right">
      <view class="toolbar-btn delete" bindtap="onBatchDelete">
        <image class="btn-icon" src="/images/svg/slash.svg" mode="aspectFit"></image>
        <text class="btn-text">删除 ({{selectedItems.length}})</text>
      </view>
    </view>
  </view>

  <!-- 历史记录列表 -->
  <scroll-view 
    class="history-list"
    scroll-y="true"
    refresher-enabled="{{!editMode}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="refreshHistory"
  >
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{groupedHistory.length === 0 && !loading}}">
      <image class="empty-image" src="{{emptyConfig.image}}" mode="aspectFit"></image>
      <view class="empty-title">{{emptyConfig.title}}</view>
      <view class="empty-subtitle">{{emptyConfig.subtitle}}</view>
    </view>

    <!-- 分组历史记录 -->
    <view class="history-groups" wx:else>
      <view class="history-group" wx:for="{{groupedHistory}}" wx:key="date">
        <!-- 日期标题 -->
        <view class="group-header">
          <view class="group-date">{{item.date}}</view>
          <view class="group-count">{{item.items.length}} 条记录</view>
        </view>

        <!-- 历史记录项 -->
        <view class="history-items">
          <view 
            class="history-item {{editMode ? 'edit-mode' : ''}} {{selectedItems.includes(historyItem.id) ? 'selected' : ''}}"
            wx:for="{{item.items}}"
            wx:for-item="historyItem"
            wx:key="id"
            data-item="{{historyItem}}"
            data-id="{{historyItem.id}}"
            bindtap="onHistoryItemTap"
          >
            <!-- 选择框 -->
            <view class="select-checkbox" wx:if="{{editMode}}">
              <view class="checkbox {{selectedItems.includes(historyItem.id) ? 'checked' : ''}}">
                <image class="check-icon" src="/images/svg/check.svg" mode="aspectFit"></image>
              </view>
            </view>

            <!-- 类型图标 -->
            <view class="type-icon">
              <image 
                class="icon-image" 
                src="{{historyItem.type === 'food' ? '/images/dish1.jpg' : historyItem.type === 'post' ? '/images/post-icon.svg' : '/images/default-avatar.png'}}"
                mode="aspectFill"
              ></image>
              <view class="type-badge {{historyItem.type}}">
                <text class="badge-text">{{historyItem.type === 'food' ? '美食' : historyItem.type === 'post' ? '动态' : '用户'}}</text>
              </view>
            </view>

            <!-- 内容信息 -->
            <view class="item-content">
              <!-- 美食记录 -->
              <view class="food-content" wx:if="{{historyItem.type === 'food'}}">
                <view class="content-title">{{historyItem.title}}</view>
                <view class="content-desc">{{historyItem.description}}</view>
                <view class="content-meta">
                  <view class="meta-item">
                    <image class="meta-icon" src="/images/star.svg" mode="aspectFit"></image>
                    <text class="meta-text">{{historyItem.rating}}</text>
                  </view>
                  <view class="meta-item">
                    <image class="meta-icon" src="/images/time.svg" mode="aspectFit"></image>
                    <text class="meta-text">{{historyItem.cookTime}}</text>
                  </view>
                </view>
              </view>

              <!-- 动态记录 -->
              <view class="post-content" wx:elif="{{historyItem.type === 'post'}}">
                <view class="content-title">{{historyItem.title}}</view>
                <view class="content-desc">{{historyItem.content}}</view>
                <view class="author-info">
                  <image class="author-avatar" src="{{historyItem.author.avatar}}" mode="aspectFill"></image>
                  <text class="author-name">{{historyItem.author.name}}</text>
                </view>
              </view>

              <!-- 用户记录 -->
              <view class="user-content" wx:elif="{{historyItem.type === 'user'}}">
                <view class="content-title">
                  <text class="user-name">{{historyItem.name}}</text>
                  <image class="verified-icon" wx:if="{{historyItem.verified}}" src="/images/svg/check.svg" mode="aspectFit"></image>
                </view>
                <view class="content-desc">{{historyItem.bio}}</view>
                <view class="user-stats">
                  <text class="stat-text">{{historyItem.followers}} 粉丝</text>
                  <text class="stat-text">{{historyItem.posts}} 动态</text>
                </view>
              </view>
            </view>

            <!-- 浏览信息 -->
            <view class="view-info">
              <view class="view-time">{{formatTime(historyItem.viewTime)}}</view>
              <view class="view-stats">
                <view class="stat-item">
                  <text class="stat-label">浏览</text>
                  <text class="stat-value">{{historyItem.viewCount}}次</text>
                </view>
                <view class="stat-item">
                  <text class="stat-label">时长</text>
                  <text class="stat-value">{{formatDuration(historyItem.duration)}}</text>
                </view>
              </view>
            </view>

            <!-- 删除按钮 -->
            <view class="delete-action" wx:if="{{!editMode}}" data-id="{{historyItem.id}}" bindtap="onDeleteItem">
              <image class="delete-icon" src="/images/svg/slash.svg" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="load-more" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>